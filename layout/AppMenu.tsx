/* eslint-disable @next/next/no-img-element */

import React, { useContext } from 'react';
import AppMenuitem from './AppMenuitem';
import { LayoutContext } from './context/layoutcontext';
import { MenuProvider } from './context/menucontext';
import Link from 'next/link';
import { AppMenuItem } from '@/types';
import { useTabContext } from '../layout/context/tabcontext';
import { useRouter } from 'next/navigation';

const AppMenu = () => {
    const router = useRouter();
    const { layoutConfig, setLayoutState } = useContext(LayoutContext);

    const { addTab } = useTabContext(); // AppMenu harus dirender di dalam TabProvider
    // tambah tab + navigate
    const addTabWrapper = (key: string, title: string, path: string) => {
        addTab({ key, title, path });
        router.push(path);
        // Close mobile/overlay menu to avoid mask blocking clicks
        setLayoutState((prev) => ({
            ...prev,
            overlayMenuActive: false,
            staticMenuMobileActive: false,
            menuHoverActive: false
        }));
    };

    // ─────────────── Menu RSC “Ribbon” ───────────────
    const model: AppMenuItem[] = [
        // ───── Main ─────
        {
            label: 'Main',
            icon: 'pi pi-fw pi-home',
            items: [
                // StartUp
                {
                    label: 'StartUp',
                    icon: 'pi pi-fw pi-sign-in',
                    items: [
                        {
                            label: 'Login/Logout',
                            icon: 'pi pi-fw pi-sign-in',
                            to: '/login'
                        }
                    ]
                },
                // Jadwal
                {
                    label: 'Jadwal',
                    icon: 'pi pi-fw pi-calendar',
                    items: [
                        {
                            label: 'Jadwal',
                            icon: 'pi pi-fw pi-calendar',
                            to: '/jadwal'
                        }
                    ]
                },
                // Muatan
                {
                    label: 'Muatan',
                    icon: 'pi pi-fw pi-box',
                    items: [
                        {
                            label: 'Order',
                            icon: 'pi pi-fw pi-box',
                            command: () => addTabWrapper('orders', 'Orders', '/orders')
                        },
                        {
                            label: 'Shipping',
                            icon: 'pi pi-fw pi-truck',
                            command: () => addTabWrapper('shipping', 'Shipping', '/shipping')
                        },
                        {
                            label: 'Berita Acara',
                            icon: 'pi pi-fw pi-file',
                            command: () => addTabWrapper('berita_acara', 'Berita Acara', '/berita_acara')
                        },
                        {
                            label: 'DKB',
                            icon: 'pi pi-fw pi-database',
                            to: '/dkb'
                        }
                    ]
                },
                // Tagihan
                {
                    label: 'Tagihan',
                    icon: 'pi pi-fw pi-wallet',
                    items: [
                        {
                            label: 'Invoice',
                            icon: 'pi pi-fw pi-inbox',
                            to: '/invoice'
                        },
                        {
                            label: 'Pembayaran',
                            icon: 'pi pi-fw pi-credit-card',
                            to: '/pembayaran'
                        },
                        {
                            label: 'Freight Pay',
                            icon: 'pi pi-fw pi-money-bill',
                            to: '/freight-pay'
                        }
                    ]
                },
                // Biaya
                {
                    label: 'Biaya',
                    icon: 'pi pi-fw pi-money-bill',
                    items: [
                        {
                            label: 'Storage',
                            icon: 'pi pi-fw pi-archive',
                            to: '/storage'
                        },
                        {
                            label: 'Demurrage',
                            icon: 'pi pi-fw pi-clock',
                            to: '/demurrage'
                        }
                    ]
                },
                // Freight
                {
                    label: 'Freight',
                    icon: 'pi pi-fw pi-ship',
                    items: [
                        {
                            label: 'Freight',
                            icon: 'pi pi-fw pi-plane',
                            to: '/freight'
                        },
                        {
                            label: 'Dooring',
                            icon: 'pi pi-fw pi-door-open',
                            to: '/dooring'
                        },
                        {
                            label: 'Trucking',
                            icon: 'pi pi-fw pi-truck',
                            to: '/trucking'
                        },
                        {
                            label: 'Claim',
                            icon: 'pi pi-fw pi-exclamation-triangle',
                            to: '/claim'
                        },
                        {
                            label: 'Asuransi',
                            icon: 'pi pi-fw pi-shield',
                            to: '/asuransi'
                        }
                    ]
                },
                // Exit
                {
                    label: 'Exit',
                    icon: 'pi pi-fw pi-sign-out',
                    items: [
                        {
                            label: 'Exit',
                            icon: 'pi pi-fw pi-power-off',
                            to: '/logout'
                        }
                    ]
                }
            ]
        },
        // ───── Management ─────
        {
            label: 'Management',
            icon: 'pi pi-fw pi-cog',
            items: [
                {
                    label: 'Initial Data',
                    icon: 'pi pi-fw pi-database',
                    items: [
                        {
                            label: 'Truk Comp',
                            icon: 'pi pi-fw pi-truck',
                            to: '/management/truk-comp'
                        },
                        {
                            label: 'Shipper',
                            icon: 'pi pi-fw pi-user',
                            to: '/management/shipper'
                        },
                        {
                            label: 'Agent',
                            icon: 'pi pi-fw pi-users',
                            to: '/management/agent'
                        },
                        {
                            label: 'Ship Comp',
                            icon: 'pi pi-fw pi-building',
                            to: '/management/ship-comp'
                        },
                        {
                            label: 'Anak Comp',
                            icon: 'pi pi-fw pi-child',
                            to: '/management/anak-comp'
                        },
                        {
                            label: 'Consignee',
                            icon: 'pi pi-fw pi-id-card',
                            to: '/management/consignee'
                        },
                        {
                            label: 'Kota Kirim',
                            icon: 'pi pi-fw pi-map-marker',
                            to: '/management/kota-kirim'
                        },
                        {
                            label: 'Salesman',
                            icon: 'pi pi-fw pi-briefcase',
                            to: '/management/salesman'
                        },
                        {
                            label: 'Container',
                            icon: 'pi pi-fw pi-box',
                            to: '/management/container'
                        },
                        {
                            label: 'Lock Tarif',
                            icon: 'pi pi-fw pi-lock',
                            to: '/management/lock-tarif'
                        }
                    ]
                }
            ]
        },
        // ───── Laporan ─────
        {
            label: 'Laporan',
            icon: 'pi pi-fw pi-file',
            items: [
                {
                    label: 'Reports',
                    icon: 'pi pi-fw pi-chart-line',
                    items: [
                        {
                            label: 'Reports',
                            icon: 'pi pi-fw pi-ship',
                            command: () => addTabWrapper('reports', 'Reports', '/reports')
                        }
                        // {
                        //     label: 'Order Summary',
                        //     icon: 'pi pi-fw pi-list',
                        //     to: '/reports/order-summary'
                        // },
                        // {
                        //     label: 'Financial',
                        //     icon: 'pi pi-fw pi-money-bill',
                        //     to: '/reports/financial'
                        // }
                    ]
                }
            ]
        },
        // ───── Utilitas ─────
        {
            label: 'Utilitas',
            icon: 'pi pi-fw pi-wrench',
            items: [
                {
                    label: 'Utilitas',
                    icon: 'pi pi-fw pi-sliders-h',
                    items: [
                        {
                            label: 'Skin',
                            icon: 'pi pi-fw pi-palette',
                            to: '/utilitas/skin'
                        },
                        {
                            label: 'Designer',
                            icon: 'pi pi-fw pi-pencil',
                            to: '/utilitas/designer'
                        },
                        {
                            label: 'Preferences',
                            icon: 'pi pi-fw pi-cog',
                            to: '/utilitas/preferences'
                        },
                        {
                            label: 'Check Update',
                            icon: 'pi pi-fw pi-refresh',
                            to: '/utilitas/check-update'
                        },
                        {
                            label: 'Database',
                            icon: 'pi pi-fw pi-database',
                            to: '/utilitas/database'
                        },
                        {
                            label: 'Find Data',
                            icon: 'pi pi-fw pi-search',
                            to: '/utilitas/find-data'
                        }
                    ]
                }
            ]
        }
    ];
    // ───────────────────────────────────────────────────

    return (
        <MenuProvider>
            <ul className="layout-menu text-sm">{model.map((item, i) => (!item?.seperator ? <AppMenuitem item={item} root={true} index={i} key={item.label} /> : <li className="menu-separator" key={`sep_${i}`} />))}</ul>
        </MenuProvider>
    );
};

export default AppMenu;
