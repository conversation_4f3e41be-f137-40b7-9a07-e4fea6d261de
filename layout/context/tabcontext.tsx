'use client';
import React, { createContext, useContext, useEffect, useMemo, useRef, useState, ReactNode } from 'react';

export interface Tab {
    key: string;
    title: string;
    path: string;
    content?: React.ReactNode;
}

interface TabContextType {
    tabs: Tab[];
    activeTab: string | null;
    addTab: (tab: Tab) => void;
    setActiveTab: (key: string) => void;
    closeTab: (key: string) => void;
    // Per-tab persisted state helpers
    getTabState: <T = unknown>(key: string) => T | undefined;
    setTabState: <T = unknown>(key: string, value: T) => void;
}

const TabContext = createContext<TabContextType | undefined>(undefined);

export const TabProvider = ({ children }: { children: ReactNode }) => {
    const [tabs, setTabs] = useState<Tab[]>([]);
    const [activeTab, setActiveTabState] = useState<string | null>(null);
    const [tabStateMap, setTabStateMap] = useState<Record<string, unknown>>({});

    // Load from sessionStorage on mount
    useEffect(() => {
        try {
            const raw = sessionStorage.getItem('tabStateMap');
            if (raw) setTabStateMap(JSON.parse(raw));
        } catch {}
    }, []);

    // Persist to sessionStorage whenever it changes
    useEffect(() => {
        try {
            sessionStorage.setItem('tabStateMap', JSON.stringify(tabStateMap));
        } catch {}
    }, [tabStateMap]);

    const addTab = (tab: Tab) => {
        setTabs((prev) => {
            if (prev.find((t) => t.key === tab.key)) return prev;
            return [...prev, tab];
        });
        setActiveTabState(tab.key);
    };

    const setActiveTab = (key: string) => {
        setActiveTabState(key);
    };

    const closeTab = (key: string) => {
        setTabs((prev) => {
            const newTabs = prev.filter((t) => t.key !== key);
            // If we're closing the active tab, set the first remaining tab as active
            if (activeTab === key && newTabs.length > 0) {
                setActiveTabState(newTabs[0].key);
            }
            return newTabs;
        });
    };

    const getTabState = <T = unknown,>(key: string): T | undefined => tabStateMap[key] as T | undefined;
    const setTabState = <T = unknown,>(key: string, value: T) => {
        setTabStateMap((prev) => ({ ...prev, [key]: value }));
    };

    return <TabContext.Provider value={{ tabs, activeTab, addTab, setActiveTab, closeTab, getTabState, setTabState }}>{children}</TabContext.Provider>;
};

export const useTabContext = () => {
    const context = useContext(TabContext);
    if (!context) throw new Error('useTabContext must be used inside TabProvider');
    return context;
};

// Simple convenience hook to bind component state to a tab key with persistence
export function useTabState<T>(tabKey: string, initialValue: T): [T, (next: T) => void] {
    const { getTabState, setTabState } = useTabContext();
    const initialResolved = useMemo(() => {
        const saved = getTabState<T>(tabKey);
        return (saved as T | undefined) ?? initialValue;
    }, [getTabState, tabKey, initialValue]);

    const [state, setState] = useState<T>(initialResolved);

    // keep session in sync whenever local state changes
    useEffect(() => {
        setTabState<T>(tabKey, state);
    }, [tabKey, state, setTabState]);

    return [state, setState];
}
