// File: rsc-app/layout/context/tabmanagercontext.tsx
'use client';

import React, { ReactNode, createContext, useContext, useState } from 'react';

// Tiap tab diwakili { key, title, content }
interface Tab {
  key: string;           // misal 'dashboard', 'orders', 'shipping'
  title: string;         // judul tab: "Dashboard", "Orders", "Shipping"
  content: ReactNode;    // komponen yg dirender di dalam TabPanel
}

interface TabManagerContextProps {
  tabs: Tab[];
  activeKey: string | null;
  openTab: (tab: Tab) => void;
  closeTab: (key: string) => void;
  setActiveKey: (key: string) => void;
}

const TabManagerContext = createContext<TabManagerContextProps | undefined>(undefined);

export const TabManagerProvider = ({ children }: { children: ReactNode }) => {
  const [tabs, setTabs] = useState<Tab[]>([]);
  const [activeKey, setActiveKey] = useState<string | null>(null);

  // Buka atau fokus tab baru
  const openTab = (tab: Tab) => {
    setTabs((prev) => {
      // Jika sudah ada, tidak ditambah; hanya set fokus
      const exists = prev.find((t) => t.key === tab.key);
      if (exists) {
        setActiveKey(tab.key);
        return prev;
      }
      setActiveKey(tab.key);
      return [...prev, tab];
    });
  };

  // Tutup tab tertentu
  const closeTab = (key: string) => {
    setTabs((prev) => {
      const filtered = prev.filter((t) => t.key !== key);
      // Jika yang ditutup adalah active, fallback ke tab pertama jika ada
      if (activeKey === key) {
        setActiveKey(filtered.length > 0 ? filtered[0].key : null);
      }
      return filtered;
    });
  };

  return (
    <TabManagerContext.Provider value={{ tabs, activeKey, openTab, closeTab, setActiveKey }}>
      {children}
    </TabManagerContext.Provider>
  );
};

export const useTabManager = (): TabManagerContextProps => {
  const ctx = useContext(TabManagerContext);
  if (!ctx) throw new Error('useTabManager must be inside TabManagerProvider');
  return ctx;
};
