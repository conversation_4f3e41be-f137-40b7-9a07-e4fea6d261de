.p-tabview-nav {
    list-style: none !important;
    padding-left: 0 !important;
    margin: 0;
    display: flex;
    align-items: center;
    border: none;
    padding-bottom: 4px;
    gap: 6px;
    background-color: #f5f9ff;
  }
  
  /* Header setiap tab */
  .p-tabview-nav li {
    margin: 0;
  }
  
  /* Link dalam tab */
  .p-tabview-nav li .p-tabview-nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 14px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    border-radius: 8px 8px 0 0;
    color: #475569; /* slate-600 */
    background: #f9fafb; /* slate-50 */
    transition: all 0.25s ease;
    position: relative;
  }
  
  /* Hover effect */
  .p-tabview-nav li .p-tabview-nav-link:hover {
    background: linear-gradient(135deg, #e0f2fe, #f0f9ff); /* biru muda gradien */
    color: #0ea5e9; /* cyan-600 */
    transform: translateY(-1px);
  }
  
  /* Tab aktif */
  .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: linear-gradient(135deg, #2563eb, #1d4ed8); /* blue gradien */
    color: #fff;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
  }
  
  /* Indicator bawah */
  .p-tabview-nav li.p-highlight .p-tabview-nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 25%;
    width: 50%;
    height: 3px;
    background: #2563eb;
    border-radius: 2px;
  }
  
  /* Close button */
  .p-tabview-nav .pi-times {
    font-size: 12px;
    margin-left: 6px;
    color: #cbd5e1; /* slate-300 */
    cursor: pointer;
    transition: all 0.2s;
    padding: 2px;
    border-radius: 50%;
  }
  
  .p-tabview-nav .pi-times:hover {
    background: #fee2e2; /* red-100 */
    color: #dc2626; /* red-600 */
    transform: scale(1.1);
  }
  
  /* Konten panel (opsional bisa disembunyikan karena kontenmu di layout) */
  .p-tabview-panels {
    display: none;
  }

  .sticky-tabmenu {
    position: sticky;
    top: 5rem;
    z-index: 996;
    background: var(--surface-card);
  }
