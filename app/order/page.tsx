'use client';

import React, { useState, useMemo } from 'react';
import { Tab<PERSON>iew, TabPanel } from 'primereact/tabview';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputNumber, InputNumberValueChangeEvent } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { InputTextarea } from 'primereact/inputtextarea';
import { Panel } from 'primereact/panel';
import { AgGridReact } from 'ag-grid-react';
import { ColDef, ModuleRegistry, AllCommunityModule } from 'ag-grid-community';
import GridComponent from '../components/GridComponent';

// DAFTARKAN MODUL AG GRID
ModuleRegistry.registerModules([AllCommunityModule]);

// Contoh interface untuk DetailBarang
interface DetailBarang {
  id: number;
  detailBarang: string;
  qty: number;
  unit: string;
  keterangan: string;
}

export default function OrderPage() {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  // ------------------ STATE UNTUK FORM “Input Order” ------------------
  const [noOrder, setNoOrder] = useState<string>('');
  const [tanggal, setTanggal] = useState<Date | null>(null);
  const [sales, setSales] = useState<number>(0);

  const [truk, setTruk] = useState<string | null>(null);
  const [noSJ, setNoSJ] = useState<string>('');
  const [plat, setPlat] = useState<string>('');
  const [supir, setSupir] = useState<string>('');
  const [notes, setNotes] = useState<string>('');

  const [shipper, setShipper] = useState<string>('');
  const [tempAmbil, setTempAmbil] = useState<string>('');
  const [consignee, setConsignee] = useState<string>('');
  const [kotaTujuan, setKotaTujuan] = useState<string>('');
  const [tempBongkar, setTempBongkar] = useState<string>('');
  const [agent, setAgent] = useState<string>('');

  const [portAsal, setPortAsal] = useState<string>('');
  const [portTujuan, setPortTujuan] = useState<string>('');
  const [shipComp, setShipComp] = useState<string>('');
  const [vessel, setVessel] = useState<string>('');
  const [voy, setVoy] = useState<string>('');
  const [etd, setEtd] = useState<Date | null>(null);
  const [tglETA, setTglETA] = useState<Date | null>(null);
  const [tglDoor, setTglDoor] = useState<Date | null>(null);
  const [kondisi, setKondisi] = useState<string>('');
  const [noContainer, setNoContainer] = useState<string>('');
  const [noSeal, setNoSeal] = useState<string>('');
  const [ukuran, setUkuran] = useState<string | null>(null);
  const [unit, setUnit] = useState<string>('');
  const [komoditi, setKomoditi] = useState<string>('');
  const [keterangan, setKeterangan] = useState<string>('');

  const [rowDataInput, setRowDataInput] = useState<DetailBarang[]>([
    { id: 1, detailBarang: 'Test', qty: 5, unit: 'pcs', keterangan: '-' }
  ]);

  const columnDefsInput: ColDef<DetailBarang>[] = [
    { field: 'detailBarang', headerName: 'Detail Barang', editable: true, flex: 3 },
    { field: 'qty', headerName: 'Qty', editable: true, flex: 1 },
    { field: 'unit', headerName: 'Unit', editable: true, flex: 1 },
    { field: 'keterangan', headerName: 'Keterangan', editable: true, flex: 2 }
  ];

  // Opsi dropdown (contoh)
  const trukOptions = [
    { label: 'LAJU', value: 'LAJU' },
    { label: 'CARGO', value: 'CARGO' },
    { label: 'TRUCK X', value: 'TRUCK_X' }
  ];

  const tujuanOptions = [
    { label: 'BATAM', value: 'BATAM' },
    { label: 'JAKARTA', value: 'JAKARTA' },
    { label: 'SURABAYA', value: 'SURABAYA' }
  ];

  // ------------------------------------------------------------------------

  return (
    <div className="p-4" style={{ backgroundColor: '#f5f5f5' }}>
      <TabView
        activeIndex={activeIndex}
        onTabChange={(e) => setActiveIndex(e.index)}
        className="custom-tabs"
      >
        {/* ==================== TAB 1: Input Order ==================== */}
        <TabPanel header="Input Order">
          <div className="mb-3 flex gap-2">
            <Button icon="pi pi-plus" label="New" />
            <Button icon="pi pi-save" label="Save" />
            <Button icon="pi pi-trash" label="Delete" severity="danger" />
            <Button icon="pi pi-pencil" label="Edit" />
            <Button icon="pi pi-angle-left" label="Prev" className="p-button-secondary" />
            <Button icon="pi pi-angle-right" label="Next" className="p-button-secondary" />
            <Button icon="pi pi-sign-out" label="Exit" className="p-button-help" />
          </div>

          <div className="grid">
            {/* Kolom 1 */}
            <div className="col-12 md:col-4">
              <Panel header="Info Order" className="mb-3">
                <div className="grid formgrid">
                  <div className="field col-12 md:col-6">
                    <label htmlFor="noOrder">No. Order</label>
                    <InputText
                      id="noOrder"
                      value={noOrder}
                      onChange={(e) => setNoOrder(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="tanggal">Tanggal</label>
                    <Calendar
                      id="tanggal"
                      value={tanggal}
                      onChange={(e) => setTanggal(e.value as Date)}
                      dateFormat="dd-mm-yy"
                      showIcon
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="sales">Sales</label>
                    <InputNumber
                      id="sales"
                      value={sales}
                      onValueChange={(e: InputNumberValueChangeEvent) =>
                        setSales(e.value || 0)
                      }
                      showButtons
                      min={0}
                      className="w-full"
                    />
                  </div>
                </div>
              </Panel>

              <Panel header="Info Transport" className="mb-3">
                <div className="grid formgrid">
                  <div className="field col-12 md:col-4">
                    <label htmlFor="truk">Truk</label>
                    <Dropdown
                      id="truk"
                      value={truk}
                      onChange={(e) => setTruk(e.value)}
                      options={[{ label: '— pilih trucking —', value: null }, ...trukOptions]}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-4">
                    <label htmlFor="noSJ">No. SJ</label>
                    <InputText
                      id="noSJ"
                      value={noSJ}
                      onChange={(e) => setNoSJ(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-4">
                    <label htmlFor="plat">Plat</label>
                    <InputText
                      id="plat"
                      value={plat}
                      onChange={(e) => setPlat(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="supir">Supir</label>
                    <InputText
                      id="supir"
                      value={supir}
                      onChange={(e) => setSupir(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="notes">Notes</label>
                    <InputTextarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      rows={3}
                      className="w-full"
                    />
                  </div>
                </div>
              </Panel>
            </div>

            {/* Kolom 2 */}
            <div className="col-12 md:col-4">
              <Panel header="Contact Information" className="mb-3">
                <div className="grid formgrid">
                  <div className="field col-12">
                    <label htmlFor="shipper">Shipper</label>
                    <InputText
                      id="shipper"
                      value={shipper}
                      onChange={(e) => setShipper(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="tempAmbil">Tmpt. Ambil</label>
                    <InputText
                      id="tempAmbil"
                      value={tempAmbil}
                      onChange={(e) => setTempAmbil(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="consignee">Consignee</label>
                    <InputText
                      id="consignee"
                      value={consignee}
                      onChange={(e) => setConsignee(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="kotaTujuan">Kota Tujuan</label>
                    <InputText
                      id="kotaTujuan"
                      value={kotaTujuan}
                      onChange={(e) => setKotaTujuan(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="tempBongkar">Tmpt. Bongkr</label>
                    <InputText
                      id="tempBongkar"
                      value={tempBongkar}
                      onChange={(e) => setTempBongkar(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12">
                    <label htmlFor="agent">Agent</label>
                    <InputText
                      id="agent"
                      value={agent}
                      onChange={(e) => setAgent(e.target.value)}
                      className="w-full"
                    />
                  </div>
                </div>
              </Panel>
            </div>

            {/* Kolom 3 */}
            <div className="col-12 md:col-4">
              <Panel header="Shipment Information" className="mb-3">
                <div className="grid formgrid">
                  <div className="field col-12 md:col-6">
                    <label htmlFor="portAsal">Port Asal</label>
                    <Dropdown
                      id="portAsal"
                      value={portAsal}
                      onChange={(e) => setPortAsal(e.value)}
                      options={tujuanOptions}
                      placeholder="— pilih asal —"
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="portTujuan">Port Tujuan</label>
                    <Dropdown
                      id="portTujuan"
                      value={portTujuan}
                      onChange={(e) => setPortTujuan(e.value)}
                      options={tujuanOptions}
                      placeholder="— pilih tujuan —"
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="shipComp">Ship Comp</label>
                    <InputText
                      id="shipComp"
                      value={shipComp}
                      onChange={(e) => setShipComp(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="vesselSI">Vessel</label>
                    <InputText
                      id="vesselSI"
                      value={vessel}
                      onChange={(e) => setVessel(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-4">
                    <label htmlFor="voy">Voy.</label>
                    <InputText
                      id="voy"
                      value={voy}
                      onChange={(e) => setVoy(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-4">
                    <label htmlFor="etd">ETD</label>
                    <Calendar
                      id="etd"
                      value={etd}
                      onChange={(e) => setEtd(e.value as Date)}
                      dateFormat="dd-mm-yy"
                      showIcon
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-4">
                    <label htmlFor="tglETA">Tgl. ETA</label>
                    <Calendar
                      id="tglETA"
                      value={tglETA}
                      onChange={(e) => setTglETA(e.value as Date)}
                      dateFormat="dd-mm-yy"
                      showIcon
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="tglDoor">Tgl. Door</label>
                    <Calendar
                      id="tglDoor"
                      value={tglDoor}
                      onChange={(e) => setTglDoor(e.value as Date)}
                      dateFormat="dd-mm-yy"
                      showIcon
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="kondisi">Kondisi</label>
                    <InputText
                      id="kondisi"
                      value={kondisi}
                      onChange={(e) => setKondisi(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="noContainer">No. Container</label>
                    <InputText
                      id="noContainer"
                      value={noContainer}
                      onChange={(e) => setNoContainer(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="noSeal">No. Seal</label>
                    <InputText
                      id="noSeal"
                      value={noSeal}
                      onChange={(e) => setNoSeal(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="ukuran">Ukuran</label>
                    <Dropdown
                      id="ukuran"
                      value={ukuran}
                      onChange={(e) => setUkuran(e.value)}
                      options={[
                        { label: '— pilih container —', value: null },
                        { label: '1 X 20', value: '1 X 20' },
                        { label: '1 X 40', value: '1 X 40' }
                      ]}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="unit">Unit</label>
                    <InputText
                      id="unit"
                      value={unit}
                      onChange={(e) => setUnit(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="field col-12 md:col-6">
                    <label htmlFor="komoditiSI">Komoditi</label>
                    <InputText
                      id="komoditiSI"
                      value={komoditi}
                      onChange={(e) => setKomoditi(e.target.value)}
                      className="w-full"
                    />
                  </div>
                </div>
              </Panel>
            </div>
          </div>

          <div className="p-fluid pt-3">
            <div className="field">
              <label htmlFor="keterangan">Keterangan</label>
              <InputTextarea
                id="keterangan"
                value={keterangan}
                onChange={(e) => setKeterangan(e.target.value || '')}
                rows={3}
                className="w-full"
              />
            </div>
          </div>

          <div className="mt-4 p-2" style={{ backgroundColor: '#fff', border: '1px solid #ccc' }}>
            <span style={{ marginRight: '2rem' }}>View Record: 11/087</span>
            <span style={{ marginRight: '2rem' }}>Saved: 02-May-2025 09:16:07</span>
            <span>Edit: 05-May-2025 09:36:56-ANGGI-ANGGI-PC</span>
          </div>
        </TabPanel>

        {/* ==================== TAB 2: Browse Data ==================== */}
        <TabPanel header="Browse Data">
          <GridComponent />
        </TabPanel>
      </TabView>

      {/* Gaya CSS untuk Tab */}
      <style jsx>{`
        .custom-tabs :global(.p-tabview-nav) {
          display: flex;
          background: #ececec;
          padding-left: 12px;
          border-bottom: none;
        }
        .custom-tabs :global(.p-tabview-nav li) {
          background: #d0d0d0;
          margin-right: 4px;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          border: 1px solid #b0b0b0;
          border-bottom: none;
          position: relative;
          top: 1px;
          min-width: 140px;
        }
        .custom-tabs :global(.p-tabview-selected) {
          background: #ffffff !important;
          border-color: #999999 !important;
          z-index: 2;
        }
        .custom-tabs :global(.p-tabview-nav li:not(.p-tabview-selected):hover) {
          background: #c8c8c8;
        }
        .custom-tabs :global(.p-tabview-nav li a) {
          color: #333333;
          font-weight: 500;
          padding: 8px 16px;
          display: inline-block;
        }
        .custom-tabs :global(.p-tabview-panels) {
          border: 1px solid #999999;
          border-top: none;
          background: #ffffff;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          padding: 16px;
        }
      `}</style>
    </div>
  );
}