'use client';
import { useState, useEffect } from 'react';

export function useFetchJson(url: string) {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    let isActive = true;
    fetch(url)
      .then((res) => res.json())
      .then((json) => {
        if (isActive) {
          setData(json);
          setLoading(false);
        }
      })
      .catch(() => {
        if (isActive) {
          setData([]);
          setLoading(false);
        }
      });
    return () => {
      isActive = false;
    };
  }, [url]);

  return { data, loading };
}
