'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputTextarea } from 'primereact/inputtextarea';
import { Panel } from 'primereact/panel';
import { Toast } from 'primereact/toast';

interface EditBeritaAcaraProps {
    id: string;
    onClose: () => void;
    onPreview?: (form: any) => void;
}

export default function EditBeritaAcara({ id, onClose, onPreview }: EditBeritaAcaraProps) {
    const [form, setForm] = useState({
        noBA: '',
        tanggal: null as Date | null,
        rangeAwal: null as Date | null,
        rangeAkhir: null as Date | null,
        noOrder: '',
        ekspedisi: '',
        pengirim: '',
        alamatPengirim: '',
        komoditi: '',
        exKapal: '',
        penerima: '',
        alamatPenerima: '',
        note: ''
    });

    const toast = useRef<any>(null);

    useEffect(() => {
        // TODO: fetch data by id dari API; untuk sekarang mock data
        // Format mock disesuaikan dengan struktur form
        const mock = {
            noBA: id ?? 'BA-0001',
            tanggal: new Date(),
            rangeAwal: new Date(),
            rangeAkhir: new Date(),
            noOrder: 'ORD-2025-01',
            ekspedisi: 'PT. Ekspedisi Contoh',
            pengirim: 'ANUGRAH PERSADA ALAM, PT',
            alamatPengirim: 'Jl. Contoh No. 1, Jakarta',
            komoditi: 'M+SUSU',
            exKapal: 'Voy. / 01 Jan 2001',
            penerima: 'EVERBRIGHT PADANG, PT',
            alamatPenerima: 'JL. RAYA PADANG BY PASS KM. 15 NO.15',
            note: 'Catatan contoh berita acara'
        };
        setForm(mock);
    }, [id]);

    const handleChange = (key: keyof typeof form, value: any) => {
        setForm((prev) => ({ ...prev, [key]: value }));
    };

    const handleSave = () => {
        // TODO: panggil API update
        console.log('Updating Berita Acara:', { id, ...form });
        toast.current?.show({ severity: 'success', summary: 'Sukses', detail: 'Berita Acara diperbarui', life: 3000 });
        onClose();
        onPreview?.(form);
    };

    const handleNew = () => {
        // reset form untuk entry baru
        setForm({
            noBA: '',
            tanggal: null,
            rangeAwal: null,
            rangeAkhir: null,
            noOrder: '',
            ekspedisi: '',
            pengirim: '',
            alamatPengirim: '',
            komoditi: '',
            exKapal: '',
            penerima: '',
            alamatPenerima: '',
            note: ''
        });
    };

    const handleDelete = () => {
        // TODO: panggil API delete
        console.log('Delete Berita Acara id:', id);
        toast.current?.show({ severity: 'warn', summary: 'Dihapus', detail: `Berita Acara ${id} dihapus`, life: 3000 });
        onClose();
    };

    const handlePrev = () => {
        // TODO: navigasi record sebelumnya
        toast.current?.show({ severity: 'info', summary: 'Prev', detail: 'Navigasi ke record sebelumnya', life: 1500 });
    };

    const handleNext = () => {
        // TODO: navigasi record selanjutnya
        toast.current?.show({ severity: 'info', summary: 'Next', detail: 'Navigasi ke record selanjutnya', life: 1500 });
    };

    return (
        <div className="mt-3">
            <Toast ref={toast} />
            <div className="mb-3 flex gap-2">
                <Button icon="pi pi-plus" label="New" onClick={handleNew} />
                <Button icon="pi pi-save" label="Save" onClick={handleSave} />
                <Button icon="pi pi-trash" label="Delete" severity="danger" onClick={handleDelete} />
                <Button icon="pi pi-angle-left" label="Prev" className="p-button-secondary" onClick={handlePrev} />
                <Button icon="pi pi-angle-right" label="Next" className="p-button-secondary" onClick={handleNext} />
                <Button icon="pi pi-sign-out" label="Exit" className="p-button-help" onClick={onClose} />
            </div>

            <div className="grid">
                {/* Kolom 1 - Data Utama */}
                <div className="col-12 md:col-6">
                    <Panel header="Data Utama" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6">
                                <label htmlFor="noBA">No. BA</label>
                                <InputText id="noBA" value={form.noBA} onChange={(e) => handleChange('noBA', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="tanggal">Tanggal</label>
                                <Calendar id="tanggal" value={form.tanggal} onChange={(e) => handleChange('tanggal', e.value)} dateFormat="dd-mm-yy" showIcon className="w-full" />
                            </div>

                            <div className="field col-12 md:col-6">
                                <label htmlFor="noOrder">No. Order</label>
                                <InputText id="noOrder" value={form.noOrder} onChange={(e) => handleChange('noOrder', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>

                            <div className="field col-12 md:col-6 flex align-items-center gap-2">
                                <label className="text-sm w-6rem">Range</label>
                                <Calendar value={form.rangeAwal} onChange={(e) => handleChange('rangeAwal', e.value)} dateFormat="dd-mm-yy" className="w-full" />
                                <span className="text-sm">s/d</span>
                                <Calendar value={form.rangeAkhir} onChange={(e) => handleChange('rangeAkhir', e.value)} dateFormat="dd-mm-yy" className="w-full" />
                            </div>

                            <div className="field col-12">
                                <label htmlFor="ekspedisi">Ekspedisi</label>
                                <InputText id="ekspedisi" value={form.ekspedisi} onChange={(e) => handleChange('ekspedisi', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>

                {/* Kolom 2 - Pengirim / Penerima */}
                <div className="col-12 md:col-6">
                    <Panel header="Pengiriman Dari (Pengirim)" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12">
                                <label htmlFor="pengirim">Pengirim</label>
                                <InputText id="pengirim" value={form.pengirim} onChange={(e) => handleChange('pengirim', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="alamatPengirim">Alamat Pengirim</label>
                                <InputText id="alamatPengirim" value={form.alamatPengirim} onChange={(e) => handleChange('alamatPengirim', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="komoditi">Komoditi</label>
                                <InputText id="komoditi" value={form.komoditi} onChange={(e) => handleChange('komoditi', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="exKapal">Ex. Kapal</label>
                                <InputText id="exKapal" value={form.exKapal} onChange={(e) => handleChange('exKapal', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                        </div>
                    </Panel>

                    <Panel header="Penerima (Consignee)" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12">
                                <label htmlFor="penerima">Penerima</label>
                                <InputText id="penerima" value={form.penerima} onChange={(e) => handleChange('penerima', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="alamatPenerima">Alamat Penerima</label>
                                <InputText id="alamatPenerima" value={form.alamatPenerima} onChange={(e) => handleChange('alamatPenerima', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                        </div>
                    </Panel>

                    <Panel header="Note" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12">
                                <label htmlFor="note">Catatan</label>
                                <InputTextarea id="note" value={form.note} onChange={(e) => handleChange('note', (e.target as HTMLTextAreaElement).value)} rows={4} className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>
            </div>

            <div className="mt-4 p-2" style={{ backgroundColor: '#fff', border: '1px solid #ccc' }}>
                <span style={{ marginRight: '2rem' }}>Record ID: {id}</span>
                <span style={{ marginRight: '2rem' }}>Last Saved: {new Date().toLocaleString()}</span>
            </div>
        </div>
    );
}
