'use client';

import React, { useRef, useState } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputTextarea } from 'primereact/inputtextarea';
import { Panel } from 'primereact/panel';
import { Toast } from 'primereact/toast';
import { OverlayPanel } from 'primereact/overlaypanel';
import { AllCommunityModule, ClientSideRowModelModule, GridReadyEvent, InfiniteRowModelModule, ModuleRegistry, TextFilterModule } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';

ModuleRegistry.registerModules([AllCommunityModule, ClientSideRowModelModule, InfiniteRowModelModule, TextFilterModule]);

interface CreateBeritaAcaraProps {
    onClose: () => void;
    onPreview?: (form: any) => void;
}
export default function CreateBeritaAcara({ onClose, onPreview }: CreateBeritaAcaraProps) {
    // state form untuk Berita Acara
    const [form, setForm] = useState({
        noBA: '',
        tanggal: null as Date | null,
        rangeAwal: null as Date | null,
        rangeAkhir: null as Date | null,
        noOrder: '',
        ekspedisi: '',
        pengirim: '',
        alamatPengirim: '',
        komoditi: '',
        exKapal: '',
        penerima: '',
        alamatPenerima: '',
        note: ''
    });

    const toast = useRef<any>(null);

    // refs untuk overlay panel (agent/shipper/consignee) jika masih ingin pakai overlay grid
    const agentOP = useRef<any>(null);
    const shipperOP = useRef<any>(null);
    const consigneeOP = useRef<any>(null);

    const showToast = (severity: string, summary: string, detail: string) => {
        toast?.current?.show({ severity, summary, detail, life: 3000 });
    };

    const handleInputChange = (key: string, value: any) => {
        setForm((prev) => ({ ...prev, [key]: value }));
    };

    const handleSave = () => {
        // TODO: Implement save logic -> panggil API atau simpan ke state parent
        console.log('Saving Berita Acara data:', form);
        showToast('success', 'Sukses', 'Berita Acara tersimpan');
        onPreview?.(form);
    };

    const handleCancel = () => {
        onClose();
    };

    // Grid ready handlers (placeholder) jika ingin isi data pada overlay grid
    const onAgentGridReady = async (params: GridReadyEvent) => {
        // Placeholder: fetch agent data dan set datasource jika perlu
    };

    const onShipperGridReady = async (params: GridReadyEvent) => {
        // Placeholder: fetch shipper data
    };

    const onConsigneeGridReady = async (params: GridReadyEvent) => {
        // Placeholder: fetch consignee data
    };

    // Selection handlers untuk overlay grid (contoh: set nilai field dan hide overlay)
    const onSelectedAgent = (event: { data: any }) => {
        const data = event.data;
        if (data) {
            // contoh: data.fNama, data.fAlamat
            handleInputChange('pengirim', data.fNama ?? form.pengirim);
            handleInputChange('alamatPengirim', data.fAlamat ?? form.alamatPengirim);
        }
        agentOP.current?.hide();
    };

    const onSelectedShipper = (event: { data: any }) => {
        const data = event.data;
        if (data) {
            // contoh mengisi shipper -> kita pakai pengirim sebagai contoh
            handleInputChange('pengirim', data.fNama ?? form.pengirim);
            handleInputChange('alamatPengirim', data.fAlamat ?? form.alamatPengirim);
        }
        shipperOP.current?.hide();
    };

    const onSelectedConsignee = (event: { data: any }) => {
        const data = event.data;
        if (data) {
            handleInputChange('penerima', data.fNama ?? form.penerima);
            handleInputChange('alamatPenerima', data.fAlamat ?? form.alamatPenerima);
        }
        consigneeOP.current?.hide();
    };

    return (
        <React.Fragment>
            <Toast ref={toast} />
            <h1 className="text-xl font-bold">Input Berita Acara</h1>

            <div className="flex gap-2 mb-3">
                <Button label="Create" icon="pi pi-plus" severity="info" size="small" disabled />
                <Button label="Save" icon="pi pi-save" severity="success" size="small" onClick={handleSave} />
                <Button label="Edit" icon="pi pi-pencil" severity="warning" size="small" disabled />
                <Button label="Delete" icon="pi pi-trash" severity="danger" size="small" disabled />
                <Button label="Cancel" icon="pi pi-times" severity="secondary" size="small" onClick={handleCancel} />
            </div>

            <div className="grid">
                {/* Kolom 1 - Input Data */}
                <div className="col-12 md:col-15">
                    <Panel header="Input Data" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-4 flex gap-2 align-items-center">
                                <label className="text-sm w-6rem">No. BA</label>
                                <InputText value={form.noBA} onChange={(e) => handleInputChange('noBA', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>

                            <div className="field col-12 md:col-4 flex gap-2 align-items-center">
                                <label className="text-sm w-6rem">Tanggal</label>
                                <Calendar value={form.tanggal} onChange={(e) => handleInputChange('tanggal', e.value)} dateFormat="dd M yy" className="w-full" />
                            </div>

                            <div className="field col-12 md:col-4 flex gap-2 align-items-center">
                                <label className="text-sm w-6rem">No. Order</label>
                                <InputText value={form.noOrder} onChange={(e) => handleInputChange('noOrder', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>

                            <div className="field col-12 md:col-6 flex gap-2 align-items-center">
                                <label className="text-sm w-6rem">Range</label>
                                <Calendar value={form.rangeAwal} onChange={(e) => handleInputChange('rangeAwal', e.value)} dateFormat="dd M yy" className="w-full" />
                                <span className="text-sm">s/d</span>
                                <Calendar value={form.rangeAkhir} onChange={(e) => handleInputChange('rangeAkhir', e.value)} dateFormat="dd M yy" className="w-full" />
                            </div>

                            <div className="field col-12 flex gap-2 align-items-center">
                                <label className="text-sm w-6rem">Ekspedisi</label>
                                <InputText value={form.ekspedisi} onChange={(e) => handleInputChange('ekspedisi', (e.target as HTMLInputElement).value)} className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>

                {/* Kolom 2 */}
                <div className="col-12 md:col-10">
                    <Panel header="Pengirim (Agent / Shipper)" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputText id="pengirim" className="w-full p-inputtext-sm" value={form.pengirim} onClick={(e) => agentOP.current?.toggle(e)} onChange={(e) => handleInputChange('pengirim', (e.target as HTMLInputElement).value)} />
                                <OverlayPanel ref={agentOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '300px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onAgentGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedAgent}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>

                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputText id="alamatPengirim" className="w-full p-inputtext-sm" value={form.alamatPengirim} onChange={(e) => handleInputChange('alamatPengirim', (e.target as HTMLInputElement).value)} />
                            </div>

                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputText id="teleponPengirim" className="w-full p-inputtext-sm" placeholder="(opsional) telepon" />
                            </div>
                        </div>
                    </Panel>

                    <Panel header="Penerima (Consignee)" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputText id="penerima" className="w-full p-inputtext-sm" value={form.penerima} onClick={(e) => consigneeOP.current?.toggle(e)} onChange={(e) => handleInputChange('penerima', (e.target as HTMLInputElement).value)} />
                                <OverlayPanel ref={consigneeOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '300px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onConsigneeGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedConsignee}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>

                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputText id="alamatPenerima" className="w-full p-inputtext-sm" value={form.alamatPenerima} onChange={(e) => handleInputChange('alamatPenerima', (e.target as HTMLInputElement).value)} />
                            </div>
                        </div>
                    </Panel>

                    <Panel header="Note" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <InputTextarea id="note" className="w-full p-inputtext-sm" value={form.note} onChange={(e) => handleInputChange('note', (e.target as HTMLTextAreaElement).value)} rows={4} autoResize placeholder="Catatan tambahan..." />
                            </div>
                        </div>
                    </Panel>
                </div>
            </div>
        </React.Fragment>
    );
}
