'use client';

import React, { useMemo, useRef, useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { TabMenu } from 'primereact/tabmenu';
import { But<PERSON> } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Toast } from 'primereact/toast';
import moment from 'moment';
import { AgGridReact } from 'ag-grid-react';
import { DateFilterModule, InfiniteRowModelModule, ModuleRegistry, NumberFilterModule, RowSelectionModule, TextFilterModule } from 'ag-grid-community';
import CreateBeritaAcara from './create';
import EditBeritaAcara from './edit';

ModuleRegistry.registerModules([NumberFilterModule, RowSelectionModule, InfiniteRowModelModule, DateFilterModule, TextFilterModule]);

export default function BeritaAcaraPage() {
    const [tab, setTab] = useState(0);
    const [editId, setEditId] = useState('');
    const [totalItems, setTotalItems] = useState(0);
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);
    const [useDateRange, setUseDateRange] = useState(false);
    const toast = useRef<any>(null);
    const gridRef = useRef<any>(null);

    // PREVIEW STATE
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [zoom, setZoom] = useState<number>(0.8); // 0.8 = 80% scale

    // cleanup blob url on unmount or change
    useEffect(() => {
        return () => {
            if (previewUrl) URL.revokeObjectURL(previewUrl);
        };
    }, [previewUrl]);

    // helper: generate simple HTML for A4 document
    const generatePreviewHtml = (formData: any) => {
        const html = `<!doctype html>
        
<html>
<head>
<meta charset="utf-8" />
<title>Berita Acara</title>
<style>
  @page { size: A4; margin: 20mm; }
  body { font-family: Arial, sans-serif; margin: 0; padding: 0; color: #111; }
  .sheet { width:210mm; min-height:297mm; padding:20mm; box-sizing:border-box; background:#fff; margin: 10mm auto; }
  .header { text-align: center; margin-bottom: 12px; }
  .meta { margin-bottom: 8px; }
  .meta .row { display:flex; gap: 8px; align-items:center; margin-bottom:6px; }
  .label { width:80px; font-weight:600; }
  .value { flex:1; border-bottom:1px dashed #ccc; padding:2px 6px; }
  .yellow { background: #fff8b3; padding:6px; display:block; }
  .table { width:100%; border-collapse: collapse; margin-top:12px; }
  .table th, .table td { border:1px solid #333; padding:6px; font-size:12px; }
  .footer { margin-top:24px; display:flex; justify-content:space-between; }
</style>
</head>
<body>
  <div class="sheet">
    <div class="header">
      <h2>BERITA ACARA</h2>
      <div style="font-size:12px">Generated preview</div>
    </div>

    <div class="meta">
      <div class="row"><div class="label">No. BA</div><div class="value">${escapeHtml(formData.noBA || '')}</div></div>
      <div class="row"><div class="label">Tanggal</div><div class="value">${formData.tanggal ? escapeHtml(moment(formData.tanggal).format('DD MMM YYYY')) : ''}</div></div>
      <div class="row"><div class="label">Range</div><div class="value">${formData.rangeAwal ? escapeHtml(moment(formData.rangeAwal).format('DD MMM YYYY')) : ''} s/d ${
            formData.rangeAkhir ? escapeHtml(moment(formData.rangeAkhir).format('DD MMM YYYY')) : ''
        }</div></div>
      <div class="row"><div class="label">No. Order</div><div class="value">${escapeHtml(formData.noOrder || '')}</div></div>
      <div class="row"><div class="label">Ekspedisi</div><div class="value">${escapeHtml(formData.ekspedisi || '')}</div></div>
    </div>

    <div style="margin-top:8px;">
      <h4>Note</h4>
      <div style="display:grid;grid-template-columns:120px 1fr; gap:8px; align-items:start;">
        <div class="label">Pengiriman Dari</div><div class="value yellow">${escapeHtml(formData.pengirim || '')}</div>
        <div class="label">Alamat</div><div class="value yellow">${escapeHtml(formData.alamatPengirim || '')}</div>
        <div class="label">Komoditi</div><div class="value yellow">${escapeHtml(formData.komoditi || '')}</div>
        <div class="label">Ex. Kapal</div><div class="value yellow">${escapeHtml(formData.exKapal || '')}</div>
        <div class="label">Penerima</div><div class="value yellow">${escapeHtml(formData.penerima || '')}</div>
        <div class="label">Alamat</div><div class="value yellow">${escapeHtml(formData.alamatPenerima || '')}</div>
      </div>
    </div>

    <table class="table">
      <thead>
        <tr><th>No</th><th>Uraian</th><th>Jumlah</th><th>Keterangan</th></tr>
      </thead>
      <tbody>
        ${Array.from({ length: 10 })
            .map((_, i) => `<tr><td>${i + 1}</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>`)
            .join('')}
      </tbody>
    </table>

    <div class="footer">
      <div>Prepared by</div>
      <div>Approved by</div>
    </div>
  </div>

<script>
  // make sure links / fonts load properly if needed
</script>
</body>
</html>`;
        return html;
    };

    // escape helper untuk mencegah injeksi raw html
    const escapeHtml = (s: any) => {
        if (!s && s !== 0) return '';
        return String(s).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    };

    // create blob url and set preview
    const setPreviewFromForm = (formData: any) => {
        const html = generatePreviewHtml(formData);
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        // me-revoke sebelummnya
        setPreviewUrl((prev) => {
            if (prev) URL.revokeObjectURL(prev);
            return url;
        });
    };

    /***** rest of your grid & browse code *****/
    const columnDefs = useMemo(
        () => [
            { headerName: 'No.', valueGetter: (p: any) => p.node.rowIndex + 1, sortable: false, filter: false },
            { headerName: 'No. BA', field: 'noBA' },
            { headerName: 'Tanggal', field: 'tanggal', filter: 'agDateColumnFilter', valueFormatter: ({ value }: any) => (value ? moment(value).format('DD-MM-YYYY') : '') },
            { headerName: 'Pengirim', field: 'pengirim' },
            { headerName: 'Ekspedisi', field: 'ekspedisi' },
            { headerName: 'Komoditi', field: 'komoditi' },
            { headerName: 'Ex. Kapal', field: 'exKapal' },
            { headerName: 'Penerima', field: 'penerima' }
        ],
        []
    );

    const defaultColDef = useMemo(
        () => ({
            sortable: true,
            resizable: false,
            filter: 'agTextColumnFilter',
            floatingFilter: true
        }),
        []
    );

    const items = useMemo(() => {
        if (editId !== '') {
            return [
                { label: 'Edit Berita Acara', icon: 'pi pi-pencil', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        } else {
            return [
                { label: 'Input Berita Acara', icon: 'pi pi-plus', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        }
    }, [editId]);

    const onGridReady = async (params: any) => {
        const rows = Array.from({ length: 50 }).map((_, idx) => ({
            noBA: `BA-${1000 + idx}`,
            tanggal: moment().add(idx, 'days').format('YYYY-MM-DD'),
            pengirim: `Pengirim ${idx}`,
            ekspedisi: `Ekspedisi ${idx % 4}`,
            komoditi: `Komoditi ${idx % 3}`,
            exKapal: `KPL-${idx}`,
            penerima: `Penerima ${idx}`
        }));
        if (params.api?.setRowData) params.api.setRowData(rows);
        else params.api.setGridOption?.('rowData', rows);
        setTotalItems(rows.length);
    };

    const onRowDoubleClicked = (params: any) => {
        setEditId(params.data?.noBA ?? '');
        setTab(0);
        // set preview for that row
        setPreviewFromForm(params.data);
    };

    const applyDateRangeToGrid = () => {
        if (!gridRef.current) return;
        const api = gridRef.current.api;
        const fm = { ...(api.getFilterModel?.() ?? {}) };
        if (!useDateRange) {
            delete fm.tanggal;
            api.setFilterModel(fm);
            return;
        }
        if (startDate && endDate) {
            fm.tanggal = { filterType: 'date', type: 'inRange', dateFrom: moment(startDate).format('YYYY-MM-DD'), dateTo: moment(endDate).format('YYYY-MM-DD') };
        } else if (startDate) {
            fm.tanggal = { filterType: 'date', type: 'greaterThan', dateFrom: moment(startDate).format('YYYY-MM-DD') };
        } else if (endDate) {
            fm.tanggal = { filterType: 'date', type: 'lessThan', dateTo: moment(endDate).format('YYYY-MM-DD') };
        }
        api.setFilterModel(fm);
    };

    const handleExportAll = () => {
        if (!gridRef.current) return;
        const api = gridRef.current.api;
        const rows: any[] = [];
        const displayed = api.getDisplayedRowCount();
        for (let i = 0; i < displayed; i++) {
            const node = api.getDisplayedRowAtIndex(i);
            if (node?.data) rows.push(node.data);
        }
        const headers = columnDefs.map((c) => `"${c.headerName}"`).join(',');
        const lines = rows.map((row) =>
            columnDefs
                .map((col) => {
                    const field = col.field as keyof typeof row;
                    const val = row[field] ?? '';
                    return `"${val}"`;
                })
                .join(',')
        );
        const csvContent = [headers, ...lines].join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'beritaacara_export.csv';
        a.click();
        URL.revokeObjectURL(url);
    };

    // print iframe content
    const handlePrint = () => {
        const iframe = document.getElementById('previewIframe') as HTMLIFrameElement | null;
        if (iframe?.contentWindow) iframe.contentWindow.print();
    };

    const handleDownloadHtml = () => {
        if (!previewUrl) return;
        const a = document.createElement('a');
        a.href = previewUrl;
        a.download = `BeritaAcara_preview.html`;
        a.click();
    };

    return (
        <>
            <Toast ref={toast} />
            <Card>
                <TabMenu model={items} activeIndex={tab} onTabChange={(e) => setTab(e.index)} className="-mt-5" />
                <div style={{ display: 'flex', gap: 12 }}>
                    <div style={{ flex: 1, minWidth: 420 }}>
                        {editId === '' && tab === 0 && <CreateBeritaAcara onClose={() => setTab(1)} onPreview={(form: any) => setPreviewFromForm(form)} />}
                        {editId !== '' && tab === 0 && (
                            <EditBeritaAcara
                                id={editId}
                                onClose={() => {
                                    setEditId('');
                                    setTab(1);
                                }}
                                onPreview={(form: any) => setPreviewFromForm(form)}
                            />
                        )}
                        {tab === 1 && (
                            <>
                                <div className="row mt-3 -mb-4 flex align-items-center">
                                    <div className="col-6">
                                        <h1 className="text-xl font-bold -ml-2">Browse Berita Acara</h1>
                                    </div>
                                    <div className="col-6 flex justify-content-end">
                                        <h1 className="text-sm">Total: {totalItems}</h1>
                                    </div>
                                </div>
                                <div className="row -mx-2 flex align-items-center">
                                    <div className="col-8 flex gap-2 align-items-center">
                                        <label className="text-sm">Tanggal</label>
                                        <Calendar value={startDate} onChange={(e) => setStartDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                        <label className="text-sm">s/d</label>
                                        <Calendar value={endDate} onChange={(e) => setEndDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                        <Button
                                            icon="pi pi-filter"
                                            size="small"
                                            onClick={() => {
                                                setUseDateRange(true);
                                                applyDateRangeToGrid();
                                            }}
                                        />
                                        <Button
                                            icon="pi pi-times"
                                            severity="secondary"
                                            size="small"
                                            onClick={() => {
                                                setUseDateRange(false);
                                                setStartDate(null);
                                                setEndDate(null);
                                                applyDateRangeToGrid();
                                            }}
                                        />
                                    </div>
                                    <div className="col-4 flex justify-content-end">
                                        <Button label="Export CSV" icon="pi pi-file" severity="info" size="small" onClick={handleExportAll} />
                                    </div>
                                </div>
                                <div className="ag-theme-alpine -mb-3" style={{ height: 'calc(100vh - 338px)', width: '100%' }}>
                                    <AgGridReact
                                        ref={gridRef}
                                        columnDefs={columnDefs}
                                        defaultColDef={defaultColDef}
                                        rowHeight={30}
                                        rowModelType="infinite"
                                        cacheBlockSize={100}
                                        maxBlocksInCache={5}
                                        rowBuffer={0}
                                        onGridReady={onGridReady}
                                        rowSelection="single"
                                        multiSortKey="ctrl"
                                        alwaysMultiSort={false}
                                        onRowDoubleClicked={onRowDoubleClicked}
                                        getRowId={(row: any) => row.noBA}
                                    />
                                </div>
                            </>
                        )}
                    </div>

                    {/* preview pane */}
                    <div style={{ width: '48%', minWidth: 380, border: '1px solid #ccc', background: '#eee', padding: 8 }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                            <div>
                                <Button icon="pi pi-search-minus" className="p-button-text" onClick={() => setZoom((z) => Math.max(0.3, z - 0.1))} />
                                <Button icon="pi pi-search-plus" className="p-button-text" onClick={() => setZoom((z) => Math.min(2, z + 0.1))} />
                                <Button icon="pi pi-file" className="p-button-text" onClick={handleDownloadHtml} />
                            </div>

                            <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                                <Button icon="pi pi-print" label="Print" onClick={handlePrint} />
                            </div>
                        </div>

                        <div style={{ width: '100%', height: 'calc(100vh - 200px)', overflow: 'auto', display: 'flex', justifyContent: 'center', alignItems: 'flex-start', padding: 8 }}>
                            {previewUrl ? (
                                <iframe
                                    id="previewIframe"
                                    title="preview"
                                    src={previewUrl}
                                    style={{
                                        width: `${210 * zoom}mm`,
                                        height: `${297 * zoom}mm`,
                                        border: '1px solid #999',
                                        boxShadow: '0 0 8px rgba(0,0,0,0.2)',
                                        background: '#fff'
                                    }}
                                />
                            ) : (
                                <div style={{ color: '#666' }}>No preview. Klik Save atau pilih record pada Browse untuk menampilkan preview.</div>
                            )}
                        </div>
                    </div>
                </div>
            </Card>
        </>
    );
}
