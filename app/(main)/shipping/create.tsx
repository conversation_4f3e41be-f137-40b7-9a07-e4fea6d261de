'use client';

import React, { useRef, useState } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { InputTextarea } from 'primereact/inputtextarea';
import { Panel } from 'primereact/panel';
import { Toast } from 'primereact/toast';
import { OverlayPanel } from 'primereact/overlaypanel';
import { AllCommunityModule, ClientSideRowModelModule, GridReadyEvent, InfiniteRowModelModule, ModuleRegistry, TextFilterModule } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';

ModuleRegistry.registerModules([AllCommunityModule, ClientSideRowModelModule, InfiniteRowModelModule, TextFilterModule]);

interface CreateShippingProps {
    onClose: () => void;
}

export default function CreateShipping({ onClose }: CreateShippingProps) {
    const [shipping, setShipping] = useState({
        noSI: '',
        tanggal: null,
        agent: '',
        alamatAgent: '',
        vessel: '',
        tujuan: '',
        komoditi: '',
        etd: null,
        party: '',
        term: '',
        ba: '',
        condition: '',
        notify: '',
        shipper: '',
        alamatShipper: '',
        teleponShipper: '',
        consignee: '',
        alamatConsignee: ''
    });

    const toast = useRef<any>(null);
    const agentOP = useRef(null);
    const shipperOP = useRef(null);
    const consigneeOP = useRef(null);

    const showToast = (severity: string, summary: string, detail: string) => {
        toast.current.show({ severity, summary, detail, life: 3000 });
    };

    const handleInputChange = (key: any, value: any) => {
        setShipping({ ...shipping, [key]: value });
    };

    const handleSave = () => {
        // TODO: Implement save logic
        console.log('Saving shipping data:', shipping);
        showToast('success', 'Success', 'Shipping instruction saved successfully');
    };

    const handleCancel = () => {
        onClose();
    };

    // Grid ready handlers (placeholder)
    const onAgentGridReady = async (params: GridReadyEvent) => {
        // Placeholder for agent grid data
    };

    const onShipperGridReady = async (params: GridReadyEvent) => {
        // Placeholder for shipper grid data
    };

    const onConsigneeGridReady = async (params: GridReadyEvent) => {
        // Placeholder for consignee grid data
    };

    // Selection handlers (placeholder)
    const onSelectedAgent = (agent: { data: any }) => {
        // Placeholder for agent selection
        (agentOP.current as any)?.hide();
    };

    const onSelectedShipper = (shipper: { data: any }) => {
        // Placeholder for shipper selection
        (shipperOP.current as any)?.hide();
    };

    const onSelectedConsignee = (consignee: { data: any }) => {
        // Placeholder for consignee selection
        (consigneeOP.current as any)?.hide();
    };

    return (
        <React.Fragment>
            <Toast ref={toast} />
            <h1 className="text-xl font-bold">Input Shipping</h1>
            <div className="flex gap-2 mb-3">
                <Button label="Create" icon="pi pi-plus" severity="info" size="small" disabled />
                <Button label="Save" icon="pi pi-save" severity="success" size="small" onClick={handleSave} />
                <Button label="Edit" icon="pi pi-pencil" severity="warning" size="small" disabled />
                <Button label="Delete" icon="pi pi-trash" severity="danger" size="small" disabled />
                <Button label="Cancel" icon="pi pi-times" severity="secondary" size="small" onClick={handleCancel} />
            </div>

            <div className="grid">
                {/* Kolom 1 - Input Data */}
                <div className="col-12 md:col-6">
                    <Panel header="Input Data" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="noSI" className="text-xs w-5rem">
                                    No. SI
                                </label>
                                <InputText id="kodeOrder" className="w-full p-inputtext-sm" size="small" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <InputText id="tgl" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="tanggal" className="text-xs w-5rem">
                                    Tanggal
                                </label>
                                <Calendar id="tanggal" className="w-full p-inputtext-sm" value={shipping.tanggal} onChange={(e) => handleInputChange('tanggal', e.value)} dateFormat="dd-mm-yy" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <InputText id="tgl" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="agent" className="text-xs w-5rem">
                                    Agent
                                </label>
                                <InputText id="agent" className="w-full p-inputtext-sm" value={shipping.agent} onClick={(e) => (agentOP.current as any)?.toggle(e)} onChange={(e) => handleInputChange('agent', e.target.value)} />
                                <OverlayPanel ref={agentOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={[{ headerName: 'Nama Agent', field: 'fNama' }] as any}
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onAgentGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedAgent}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1 justify-content-end">
                                <InputText id="tgl" className="w-5 p-inputtext-sm" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="alamatAgent" className="text-xs w-5rem">
                                    Alamat Agent
                                </label>
                                <InputText id="alamatAgent" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="kotaAgent" className="text-xs w-5rem">
                                    Kota Agent
                                </label>
                                <InputText id="alamatAgent" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="vessel" className="text-xs w-5rem">
                                    Vessel
                                </label>
                                <InputText id="vessel" className="w-full p-inputtext-sm" value={shipping.vessel} onChange={(e) => handleInputChange('vessel', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="tujuan" className="text-xs w-5rem">
                                    Tujuan
                                </label>
                                <InputText id="tujuan" className="w-full p-inputtext-sm" value={shipping.tujuan} onChange={(e) => handleInputChange('tujuan', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="komoditi" className="text-xs w-5rem">
                                    Komoditi
                                </label>
                                <InputText id="komoditi" className="w-full p-inputtext-sm" value={shipping.komoditi} onChange={(e) => handleInputChange('komoditi', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="etd" className="text-xs w-5rem">
                                    ETD JKT
                                </label>
                                <Calendar id="etd" className="w-full p-inputtext-sm" value={shipping.etd} onChange={(e) => handleInputChange('etd', e.value)} dateFormat="dd-mm-yy" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="party" className="text-xs w-5rem">
                                    Party
                                </label>
                                <InputText id="party" className="w-full p-inputtext-sm" value={shipping.party} onChange={(e) => handleInputChange('party', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="term" className="text-xs w-5rem">
                                    Term
                                </label>
                                <InputText id="term" className="w-full p-inputtext-sm" value={shipping.term} onChange={(e) => handleInputChange('term', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="ba" className="text-xs w-5rem">
                                    B/L
                                </label>
                                <InputText id="ba" className="w-full p-inputtext-sm" value={shipping.ba} onChange={(e) => handleInputChange('ba', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="condition" className="text-xs w-5rem">
                                    Condition
                                </label>
                                <InputText id="condition" className="w-full p-inputtext-sm" value={shipping.condition} onChange={(e) => handleInputChange('condition', e.target.value)} />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="notify" className="text-xs w-5rem">
                                    Notify
                                </label>
                                <InputText id="notify" className="w-full p-inputtext-sm" value={shipping.notify} onChange={(e) => handleInputChange('notify', e.target.value)} />
                            </div>
                        </div>
                    </Panel>
                </div>

                {/* Kolom 2 */}
                <div className="col-12 md:col-6">
                    <Panel header="Agent Editan" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label htmlFor="shipper" className="text-xs w-5rem">
                                </label> */}
                                <InputText id="shipper" className="w-full p-inputtext-sm" value={shipping.shipper} onClick={(e) => (shipperOP.current as any)?.toggle(e)} onChange={(e) => handleInputChange('shipper', e.target.value)} />
                                <OverlayPanel ref={shipperOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' },
                                                    { headerName: 'Telepon', field: 'fTelepon' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onShipperGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedShipper}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="alamatShipper" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="teleponShipper" className="w-full p-inputtext-sm" value={shipping.teleponShipper} onChange={(e) => handleInputChange('teleponShipper', e.target.value)} />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="alamatShipper" className="w-full p-inputtext-sm" />
                            </div>
                        </div>
                    </Panel>

                    <Panel header="Shipper Editan" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label htmlFor="consignee" className="text-xs w-5rem">
                                    Nama
                                </label> */}
                                <InputText id="consignee" className="w-full p-inputtext-sm" value={shipping.consignee} onClick={(e) => (consigneeOP.current as any)?.toggle(e)} onChange={(e) => handleInputChange('consignee', e.target.value)} />
                                <OverlayPanel ref={consigneeOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onConsigneeGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedConsignee}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="alamatConsignee" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="alamatConsignee" className="w-full p-inputtext-sm" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label></label> */}
                                <InputText id="alamatConsignee" className="w-full p-inputtext-sm" />
                            </div>
                        </div>
                    </Panel>
                    <Panel header="Note" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                {/* <label htmlFor="note" className="text-xs w-5rem">Note</label> */}
                                <InputTextarea id="note" className="w-full p-inputtext-sm" onChange={(e) => handleInputChange('note', e.target.value)} rows={3} autoResize placeholder="Catatan tambahan..." />
                            </div>
                        </div>
                    </Panel>
                </div>
            </div>
        </React.Fragment>
    );
}
