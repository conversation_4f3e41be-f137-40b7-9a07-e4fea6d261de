'use client';

import React, { useMemo, useRef, useState } from 'react';
import { Card } from 'primereact/card';
import { TabMenu } from 'primereact/tabmenu';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Toast } from 'primereact/toast';
import moment from 'moment';
import { AgGridReact } from 'ag-grid-react';
import { DateFilterModule, InfiniteRowModelModule, ModuleRegistry, NumberFilterModule, RowSelectionModule, TextFilterModule } from 'ag-grid-community';
import CreateShipping from './create';
import EditShipping from './edit';

ModuleRegistry.registerModules([NumberFilterModule, RowSelectionModule, InfiniteRowModelModule, DateFilterModule, TextFilterModule]);

export default function ShippingPage() {
    const [tab, setTab] = useState(0);
    const handleTabChange = (e: { index: number }) => setTab(e.index);

    const [editId, setEditId] = useState('');
    const [totalItems, setTotalItems] = useState(0);

    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);
    const [useDateRange, setUseDateRange] = useState(false);

    const toast = useRef<any>(null);
    const gridRef = useRef<any>(null);

    const columnDefs = useMemo(
        () => [
            { headerName: 'No.', valueGetter: (p: any) => p.node.rowIndex + 1, sortable: false, filter: false },
            { headerName: 'No. SI', field: 'siNo' },
            { headerName: 'Tanggal', field: 'kodeOrder' },
            { headerName: 'Agent', field: 'vessel' },
            { headerName: 'Vessel', field: 'voyage' },
            { headerName: 'Voyage', field: 'etd', filter: 'agDateColumnFilter', valueFormatter: ({ value }: any) => (value ? moment(value).format('DD-MM-YYYY') : '') },
            { headerName: 'Tujuan', field: 'eta', filter: 'agDateColumnFilter', valueFormatter: ({ value }: any) => (value ? moment(value).format('DD-MM-YYYY') : '') }
            // { headerName: 'No. Container', field: 'noContainer' },
            // { headerName: 'No. Seal', field: 'noSeal' },
            // { headerName: 'Shipper', field: 'namaCust' },
            // { headerName: 'Consignee', field: 'namaCons' },
            // { headerName: 'Kota Tujuan', field: 'kotaTujuan' },
            // { headerName: 'Status', field: 'status' }
        ],
        []
    );

    const defaultColDef = useMemo(
        () => ({
            sortable: true,
            resizable: false,
            filter: 'agTextColumnFilter',
            floatingFilter: true
        }),
        []
    );

    const items = useMemo(() => {
        if (editId !== '') {
            return [
                { label: 'Edit Shipping', icon: 'pi pi-pencil', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        } else {
            return [
                { label: 'Input Shipping', icon: 'pi pi-plus', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        }
    }, [editId]);

    const showToast = (severity: string, summary: string, detail: string) => {
        toast.current?.show({ severity, summary, detail, life: 3000 });
    };

    const onGridReady = async (params: any) => {
        // Mock data; nanti bisa diganti API call
        const rows = Array.from({ length: 50 }).map((_, idx) => ({
            noSI: `SI-${1000 + idx}`,
            tanggal: moment().add(idx, 'days').format('YYYY-MM-DD'),
            agent: `Agent ${idx}`,
            vessel: `Vessel ${idx % 5}`,
            voyage: `VY-${idx}`,
            tujuan: moment().add(idx, 'days').format('YYYY-MM-DD')
            // etd: moment().subtract(idx, 'days').format('YYYY-MM-DD'),
            // noContainer: `CONT-${3000 + idx}`,
            // noSeal: `SEAL-${4000 + idx}`,
            // namaCust: `Shipper ${idx}`,
            // namaCons: `Consignee ${idx}`,
            // kotaTujuan: `Kota ${idx % 4}`,
            // status: idx % 2 === 0 ? 'On Board' : 'Arrived'
        }));

        params.api.setGridOption('rowData', rows);
        setTotalItems(rows.length);
    };

    const onRowDoubleClicked = (params: any) => {
        setEditId(params.data.sjNo);
        setTab(0);
    };

    const applyDateRangeToGrid = () => {
        if (!gridRef.current) return;
        const api = gridRef.current.api;
        const fm = { ...(api.getFilterModel?.() ?? {}) };
        if (!useDateRange) {
            delete fm.etd;
            api.setFilterModel(fm);
            return;
        }

        if (startDate && endDate) {
            fm.etd = { filterType: 'date', type: 'inRange', dateFrom: moment(startDate).format('YYYY-MM-DD'), dateTo: moment(endDate).format('YYYY-MM-DD') };
        } else if (startDate) {
            fm.etd = { filterType: 'date', type: 'greaterThan', dateFrom: moment(startDate).format('YYYY-MM-DD') };
        } else if (endDate) {
            fm.etd = { filterType: 'date', type: 'lessThan', dateTo: moment(endDate).format('YYYY-MM-DD') };
        }

        api.setFilterModel(fm);
    };

    const handleExportAll = () => {
        if (!gridRef.current) return;
        const api = gridRef.current.api;
        const rows: any[] = [];
        const displayed = api.getDisplayedRowCount();
        for (let i = 0; i < displayed; i++) {
            const node = api.getDisplayedRowAtIndex(i);
            if (node?.data) rows.push(node.data);
        }

        const headers = columnDefs.map((c) => `"${c.headerName}"`).join(',');
        const lines = rows.map((row) =>
            columnDefs
                .map((col) => {
                    const field = col.field as keyof typeof row;
                    const val = row[field] ?? '';
                    return `"${val}"`;
                })
                .join(',')
        );

        const csvContent = [headers, ...lines].join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'shipping_export.csv';
        a.click();
        URL.revokeObjectURL(url);
    };

    return (
        <>
            <Toast ref={toast} />
            <Card>
                <TabMenu model={items} activeIndex={tab} onTabChange={handleTabChange} className="-mt-5" />
                {editId === '' && tab === 0 && <CreateShipping onClose={() => setTab(1)} />}
                {editId !== '' && tab === 0 && (
                    <EditShipping
                        id={editId}
                        onClose={() => {
                            setEditId('');
                            setTab(1);
                        }}
                    />
                )}
                {tab === 1 && (
                    <>
                        <div className="row mt-3 -mb-4 flex align-items-center">
                            <div className="col-6">
                                <h1 className="text-xl font-bold -ml-2">Browse Shipping</h1>
                            </div>
                            <div className="col-6 flex justify-content-end">
                                <h1 className="text-sm">Total: {totalItems}</h1>
                            </div>
                        </div>
                        <div className="row -mx-2 flex align-items-center">
                            <div className="col-8 flex gap-2 align-items-center">
                                <label className="text-sm">Tanggal</label>
                                <Calendar value={startDate} onChange={(e) => setStartDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                <label className="text-sm">s/d</label>
                                <Calendar value={endDate} onChange={(e) => setEndDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                <Button
                                    label="Filter"
                                    icon="pi pi-filter"
                                    size="small"
                                    onClick={() => {
                                        setUseDateRange(true);
                                        applyDateRangeToGrid();
                                    }}
                                />
                                <Button
                                    label="Clear"
                                    icon="pi pi-times"
                                    severity="secondary"
                                    size="small"
                                    onClick={() => {
                                        setUseDateRange(false);
                                        setStartDate(null);
                                        setEndDate(null);
                                        applyDateRangeToGrid();
                                    }}
                                />
                            </div>
                            <div className="col-4 flex justify-content-end">
                                <Button label="Export CSV" icon="pi pi-file" severity="info" size="small" onClick={handleExportAll} />
                            </div>
                        </div>
                        <div className="ag-theme-alpine -mb-3" style={{ height: 'calc(100vh - 338px)', width: '100%' }}>
                            <AgGridReact
                                ref={gridRef}
                                columnDefs={columnDefs}
                                defaultColDef={defaultColDef}
                                rowHeight={30}
                                rowModelType="infinite"
                                cacheBlockSize={100}
                                maxBlocksInCache={5}
                                rowBuffer={0}
                                onGridReady={onGridReady}
                                rowSelection="single"
                                multiSortKey="ctrl"
                                alwaysMultiSort={false}
                                onRowDoubleClicked={onRowDoubleClicked}
                                getRowId={(row: any) => row.sjNo}
                                getRowStyle={(params: any) => {
                                    const isSelected = params.node.isSelected();
                                    const isEvenRow = params.node.rowIndex % 2 === 0;
                                    if (isSelected) return { background: '#d0ebff' };
                                    if (!isEvenRow) return { background: '#f9f9f9' };
                                    return { background: '#ffffff' };
                                }}
                            />
                        </div>
                    </>
                )}
            </Card>
        </>
    );
}
