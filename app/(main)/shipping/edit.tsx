'use client';

import React, { useState, useEffect } from 'react';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import { Panel } from 'primereact/panel';

interface EditShippingProps {
    id: string;
    onClose: () => void;
}

export default function EditShipping({ id, onClose }: EditShippingProps) {
    // Form state
    const [sjNo, setSjNo] = useState<string>('');
    const [kodeOrder, setKodeOrder] = useState<string>('');
    const [vessel, setVessel] = useState<string>('');
    const [voyage, setVoyage] = useState<string>('');
    const [etd, setEtd] = useState<Date | null>(null);
    const [eta, setEta] = useState<Date | null>(null);
    const [noContainer, setNoContainer] = useState<string>('');
    const [noSeal, setNoSeal] = useState<string>('');
    const [namaCust, setNamaCust] = useState<string>('');
    const [namaCons, setNamaCons] = useState<string>('');
    const [kotaTujuan, setKotaTujuan] = useState<string>('');
    const [status, setStatus] = useState<string | null>(null);
    const [keterangan, setKeterangan] = useState<string>('');

    // Dropdown options
    const statusOptions = [
        { label: 'On Board', value: 'On Board' },
        { label: 'Arrived', value: 'Arrived' },
        { label: 'In Transit', value: 'In Transit' },
        { label: 'Delivered', value: 'Delivered' }
    ];

    // Load data on mount
    useEffect(() => {
        // TODO: Fetch shipping data by ID
        // Mock data for now
        setSjNo(id);
        setKodeOrder('ORD-2001');
        setVessel('Vessel 1');
        setVoyage('VY-1');
        setEtd(new Date());
        setEta(new Date());
        setNoContainer('CONT-3001');
        setNoSeal('SEAL-4001');
        setNamaCust('Shipper 1');
        setNamaCons('Consignee 1');
        setKotaTujuan('Kota 1');
        setStatus('On Board');
        setKeterangan('Sample shipping data');
    }, [id]);

    const handleSave = () => {
        // TODO: Implement update logic
        console.log('Updating shipping data:', {
            id,
            sjNo,
            kodeOrder,
            vessel,
            voyage,
            etd,
            eta,
            noContainer,
            noSeal,
            namaCust,
            namaCons,
            kotaTujuan,
            status,
            keterangan
        });
        onClose();
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <div className="mt-3">
            <div className="mb-3 flex gap-2">
                <Button icon="pi pi-plus" label="New" />
                <Button icon="pi pi-save" label="Save" onClick={handleSave} />
                <Button icon="pi pi-trash" label="Delete" severity="danger" />
                <Button icon="pi pi-pencil" label="Edit" />
                <Button icon="pi pi-angle-left" label="Prev" className="p-button-secondary" />
                <Button icon="pi pi-angle-right" label="Next" className="p-button-secondary" />
                <Button icon="pi pi-sign-out" label="Exit" className="p-button-help" onClick={handleCancel} />
            </div>

            <div className="grid">
                {/* Kolom 1 - Basic Info */}
                <div className="col-12 md:col-4">
                    <Panel header="Shipping Information" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6">
                                <label htmlFor="sjNo">No. SJ</label>
                                <InputText id="sjNo" value={sjNo} onChange={(e) => setSjNo(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="kodeOrder">Kode Order</label>
                                <InputText id="kodeOrder" value={kodeOrder} onChange={(e) => setKodeOrder(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="vessel">Vessel</label>
                                <InputText id="vessel" value={vessel} onChange={(e) => setVessel(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="voyage">Voyage</label>
                                <InputText id="voyage" value={voyage} onChange={(e) => setVoyage(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="etd">ETD</label>
                                <Calendar id="etd" value={etd} onChange={(e) => setEtd(e.value as Date)} dateFormat="dd-mm-yy" showIcon className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="eta">ETA</label>
                                <Calendar id="eta" value={eta} onChange={(e) => setEta(e.value as Date)} dateFormat="dd-mm-yy" showIcon className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>

                {/* Kolom 2 - Container Info */}
                <div className="col-12 md:col-4">
                    <Panel header="Container Information" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6">
                                <label htmlFor="noContainer">No. Container</label>
                                <InputText id="noContainer" value={noContainer} onChange={(e) => setNoContainer(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12 md:col-6">
                                <label htmlFor="noSeal">No. Seal</label>
                                <InputText id="noSeal" value={noSeal} onChange={(e) => setNoSeal(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="status">Status</label>
                                <Dropdown id="status" value={status} onChange={(e) => setStatus(e.value)} options={statusOptions} placeholder="Select Status" className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>

                {/* Kolom 3 - Contact Info */}
                <div className="col-12 md:col-4">
                    <Panel header="Contact Information" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12">
                                <label htmlFor="namaCust">Shipper</label>
                                <InputText id="namaCust" value={namaCust} onChange={(e) => setNamaCust(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="namaCons">Consignee</label>
                                <InputText id="namaCons" value={namaCons} onChange={(e) => setNamaCons(e.target.value)} className="w-full" />
                            </div>
                            <div className="field col-12">
                                <label htmlFor="kotaTujuan">Kota Tujuan</label>
                                <InputText id="kotaTujuan" value={kotaTujuan} onChange={(e) => setKotaTujuan(e.target.value)} className="w-full" />
                            </div>
                        </div>
                    </Panel>
                </div>
            </div>

            <div className="p-fluid pt-3">
                <div className="field">
                    <label htmlFor="keterangan">Keterangan</label>
                    <InputTextarea id="keterangan" value={keterangan} onChange={(e) => setKeterangan(e.target.value || '')} rows={3} className="w-full" />
                </div>
            </div>

            <div className="mt-4 p-2" style={{ backgroundColor: '#fff', border: '1px solid #ccc' }}>
                <span style={{ marginRight: '2rem' }}>View Record: 1/1</span>
                <span style={{ marginRight: '2rem' }}>Saved: {new Date().toLocaleString()}</span>
                <span>Edit: {new Date().toLocaleString()}</span>
            </div>
        </div>
    );
}
