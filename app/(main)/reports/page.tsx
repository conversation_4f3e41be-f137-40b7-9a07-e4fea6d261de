"use client";

import { Card } from "primereact/card";
import { Panel } from "primereact/panel";
import { useRouter } from "next/navigation";

export default function ReportsPage() {
  const router = useRouter();

  return (
    <div className="p-4">
      <Card title="Reports">
        <Panel header="Laporan Order" toggleable>
          <div className="flex flex-column gap-3">
            <div
              className="p-3 border-round border-1 surface-border hover:surface-100 cursor-pointer transition-colors transition-duration-150"
              onClick={() => router.push("/reports/order-summary")}
            >
              <div className="flex align-items-center gap-3">
                <i className="pi pi-file text-xl" />
                <span className="font-medium">Order Summary Report</span>
              </div>
            </div>

            <div
              className="p-3 border-round border-1 surface-border hover:surface-100 cursor-pointer transition-colors transition-duration-150"
              onClick={() => router.push("/reports/shipping")}
            >
              <div className="flex align-items-center gap-3">
                <i className="pi pi-file text-xl" />
                <span className="font-medium">Shipping Report</span>
              </div>
            </div>

            <div
              className="p-3 border-round border-1 surface-border hover:surface-100 cursor-pointer transition-colors transition-duration-150"
              onClick={() => router.push("/reports/financial")}
            >
              <div className="flex align-items-center gap-3">
                <i className="pi pi-file text-xl" />
                <span className="font-medium">Financial Report</span>
              </div>
            </div>
          </div>
        </Panel>
      </Card>
    </div>
  );
}
