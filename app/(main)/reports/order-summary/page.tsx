'use client';

import { useMemo, useRef, useState } from 'react';
import moment from 'moment';
import { AgGridReact } from 'ag-grid-react';
import {
  DateFilterModule,
  InfiniteRowModelModule,
  ModuleRegistry,
  NumberFilterModule,
  RowSelectionModule,
  TextFilterModule,
  type GridReadyEvent,
} from 'ag-grid-community';

import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Checkbox } from 'primereact/checkbox';
import { Toast } from 'primereact/toast';
import { useRouter } from 'next/navigation';

import { fetchOrders } from '@/services/orderService';

// daftar modul AG Grid
ModuleRegistry.registerModules([
  NumberFilterModule,
  RowSelectionModule,
  InfiniteRowModelModule,
  DateFilterModule,
  TextFilterModule,
]);

export default function OrderSummaryReport() {
  const router = useRouter();
  const toast = useRef<any>(null);
  const gridRef = useRef<any>(null);

  const [totalItems, setTotalItems] = useState(0);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [useDateRange, setUseDateRange] = useState(false);

  const showToast = (severity: string, summary: string, detail: string) =>
    toast.current?.show({ severity, summary, detail, life: 3000 });

  const columnDefs = useMemo(
    () => [
      {
        headerName: 'No.',
        valueGetter: (p: any) => p.node.rowIndex + 1,
        sortable: false,
        filter: false,
        width: 80,
      },
      { headerName: 'Kode Order', field: 'kodeOrder' },
      {
        headerName: 'Tgl Order',
        field: 'tgl',
        filter: 'agDateColumnFilter',
        valueFormatter: ({ value }: any) =>
          value ? moment(value).format('DD-MM-YYYY') : '',
      },
      { headerName: 'Tempat Ambil', field: 'tempatCust' },
      { headerName: 'Tempat Bongkar', field: 'tmptBongkar' },
      { headerName: 'Plat Mobil', field: 'platTruk' },
      { headerName: 'Supir', field: 'supir' },
      { headerName: 'Komoditi', field: 'komoditi' },
      { headerName: 'Ukuran', field: 'namaContainer' },
      { headerName: 'No. Container', field: 'noContainer' },
      { headerName: 'No. Seal', field: 'noSeal' },
      { headerName: 'Load Sheet', field: 'sheet' },
      { headerName: 'Shipper', field: 'namaCust' },
      { headerName: 'Consignee', field: 'namaCons' },
      { headerName: 'Kota Tujuan', field: 'kotaTujuan' },
      { headerName: 'Agent', field: 'namaAgent' },
      { headerName: 'Vessel', field: 'vessel' },
      { headerName: 'Voyage', field: 'voyage' },
      { headerName: 'Port Asal', field: 'kotaAsal' },
      { headerName: 'Port Tujuan', field: 'kotaCons' },
      {
        headerName: 'Tgl. TD',
        field: 'etd',
        filter: 'agDateColumnFilter',
        valueFormatter: ({ value }: any) =>
          value ? moment(value).format('DD-MM-YYYY') : '',
      },
      {
        headerName: 'Tgl. TA',
        field: 'eta',
        filter: 'agDateColumnFilter',
        valueFormatter: ({ value }: any) =>
          value ? moment(value).format('DD-MM-YYYY') : '',
      },
      {
        headerName: 'Tgl. Dooring',
        field: 'tglDooring',
        filter: 'agDateColumnFilter',
        valueFormatter: ({ value }: any) =>
          value ? moment(value).format('DD-MM-YYYY') : '',
      },
      { headerName: 'Keterangan', field: 'ket' },
      { headerName: 'Shipping Comp', field: 'namaLine' },
      { headerName: 'No. SJ', field: 'sjNo' },
    ],
    []
  );

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: false,
      filter: 'agTextColumnFilter',
      floatingFilter: true,
    }),
    []
  );

  const onGridReady = async (params: GridReadyEvent) => {
    const dataSource = {
      getRows: async (p: any) => {
        const { startRow, endRow, filterModel, sortModel } = p;
        try {
          const res = await fetchOrders(startRow, endRow, filterModel, sortModel);
          const rows = res.data;
          const lastRow = res.total <= endRow ? res.total : -1;

          setTotalItems(res.total);
          p.successCallback(rows, lastRow);
        } catch (e) {
          console.error(e);
          p.failCallback();
          showToast('error', 'Error', 'Failed to fetch orders');
        }
      },
    };
    params.api.setGridOption('datasource', dataSource);
  };

  const applyDateRangeToGrid = () => {
    const api = gridRef.current?.api;
    if (!api) return;

    const fm = { ...(api.getFilterModel?.() ?? {}) };

    if (!useDateRange) {
      delete fm.tgl;
      api.setFilterModel(fm);
      return;
    }

    const hasStart = !!startDate;
    const hasEnd = !!endDate;

    if (!hasStart && !hasEnd) {
      delete fm.tgl;
    } else if (hasStart && hasEnd) {
      fm.tgl = {
        filterType: 'date',
        type: 'inRange',
        dateFrom: moment(startDate!).format('YYYY-MM-DD'),
        dateTo: moment(endDate!).format('YYYY-MM-DD'),
      };
    } else if (hasStart) {
      fm.tgl = {
        filterType: 'date',
        type: 'greaterThan',
        dateFrom: moment(startDate!).format('YYYY-MM-DD'),
      };
    } else {
      fm.tgl = {
        filterType: 'date',
        type: 'lessThan',
        dateTo: moment(endDate!).format('YYYY-MM-DD'),
      };
    }
    api.setFilterModel(fm);
  };

  const handleExportAll = async () => {
    try {
      const api = gridRef.current?.api;
      const filterModel = api?.getFilterModel?.() ?? {};
      const sortModel = api?.getSortModel?.() ?? [];

      const res = await fetchOrders(0, totalItems, filterModel, sortModel);
      const rows = res.data ?? [];

      const headers = (columnDefs as any[]).map((c) => `"${c.headerName ?? ''}"`).join(',');
      const lines = rows
        .map((row: any) =>
          (columnDefs as any[]).map((col) => {
            const field = col.field as string | undefined;
            let val = field ? row[field] : '';
            if (col.valueFormatter && field) {
              try {
                val = col.valueFormatter({ value: row[field] });
              } catch {}
            }
            return `"${(val ?? '').toString().replace(/"/g, '""')}"`;
          }).join(',')
        );

      const csvContent = [headers, ...lines].join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'order_summary.csv';
      a.click();
      URL.revokeObjectURL(url);
    } catch {
      showToast('error', 'Export Failed', 'Gagal export data');
    }
  };

  return (
    <div className="p-4">
      <Toast ref={toast} />
      <Card title="Order Summary Report" subTitle="View and export order summary data">
        <div className="flex justify-content-between align-items-center mb-3">
          <Button
            label="Back to Reports"
            icon="pi pi-arrow-left"
            className="p-button-text"
            onClick={() => router.push('/reports')}
          />
          <div className="text-sm">Total: {totalItems}</div>
        </div>

        <div className="row -mx-2 flex align-items-center mb-2">
          <div className="col-8 flex gap-2 align-items-center">
            <label htmlFor="startDate" className="text-sm">Tanggal</label>
            <Calendar
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.value as Date | null)}
              dateFormat="dd-mm-yy"
              className="p-inputtext-sm"
            />
            <label htmlFor="endDate" className="text-sm">s/d</label>
            <Calendar
              value={endDate}
              onChange={(e) => setEndDate(e.value as Date | null)}
              dateFormat="dd-mm-yy"
              className="p-inputtext-sm"
            />
            <Checkbox
              inputId="useDateFilter"
              checked={useDateRange}
              onChange={(e) => {
                setUseDateRange(e.checked!);
                applyDateRangeToGrid();
              }}
            />
            <label htmlFor="useDateFilter" className="text-sm">Filter Tanggal</label>
          </div>
          <div className="col-4 flex justify-content-end">
            <Button label="Export CSV" icon="pi pi-file" severity="info" size="small" onClick={handleExportAll} />
          </div>
        </div>

        <div className="ag-theme-alpine" style={{ height: 'calc(100vh - 338px)', width: '100%' }}>
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs as any}
            defaultColDef={defaultColDef}
            rowHeight={30}
            rowModelType="infinite"
            cacheBlockSize={100}
            maxBlocksInCache={5}
            rowBuffer={0}
            onGridReady={onGridReady}
            rowSelection="single"
            multiSortKey="ctrl"
            alwaysMultiSort={false}
            getRowId={(r: any) => r.kodeOrder}
            getRowStyle={(p: any) => {
              const isSelected = p.node.isSelected();
              const isEven = p.node.rowIndex % 2 === 0;
              if (isSelected) return { background: '#d0ebff' };
              if (!isEven) return { background: '#f9f9f9' };
              return { background: '#ffffff' };
            }}
          />
        </div>
      </Card>
    </div>
  );
}
