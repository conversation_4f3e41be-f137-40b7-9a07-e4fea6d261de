'use client'

import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';
import { useRouter } from 'next/navigation';

export default function ShippingReport() {
  const router = useRouter();
  return (
    <div className="p-4">
      <Card
        title="Shipping Report"
        subTitle="View and export shipping data"
      >
        <div className="flex flex-column gap-4">
            </div>
          <div className="flex flex-column gap-4">
          <Button
            label="Back to Reports"
            icon="pi pi-arrow-left"
            className="p-button-text"
            onClick={() => router.push('/reports')}
          />
          <div className="flex justify-content-between align-items-center">
            <span>Report content will be displayed here</span>
            <Button
              label="Export to Excel"
              icon="pi pi-file-excel"
              className="p-button-outlined"
            />
          </div>
        </div>
      </Card>
    </div>
  );
}
