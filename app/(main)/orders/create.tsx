import { getAgents } from '@/services/agentService';
import { getConsignees } from '@/services/consigneeService';
import { getContainers } from '@/services/containerService';
import { getCustomers } from '@/services/customerService';
import { getJadwal } from '@/services/jadwalService';
import { getKota } from '@/services/kotaService';
import { getNomor } from '@/services/nomorService';
import { createOrder } from '@/services/orderService';
import { getSales } from '@/services/salesService';
import { getShippingComps } from '@/services/shippingCompService';
import { getTruk } from '@/services/trukService';
import { AllCommunityModule, ClientSideRowModelModule, GridReadyEvent, InfiniteRowModelModule, ModuleRegistry, TextFilterModule } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import moment from 'moment';
import { But<PERSON> } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { OverlayPanel } from 'primereact/overlaypanel';
import { Panel } from 'primereact/panel';
import { Toast } from 'primereact/toast';
import React, { useCallback, useEffect, useRef, useState } from 'react';

ModuleRegistry.registerModules([AllCommunityModule, ClientSideRowModelModule, InfiniteRowModelModule, TextFilterModule]);

const CreateOrder = ({ onClose }: { onClose: () => void }) => {
    const [order, setOrder] = useState({
        kodeOrder: '',
        tgl: '',
        sales: {
            kodeSales: 0,
            namaSales: ''
        },
        truk: {
            kodeTruk: 0
        },
        trukComp: {
            kodeComp: 0
        },
        sjNo: '',
        platTruk: '',
        supir: '',
        sheet: '',
        tmptBongkar: '',
        shipper: {
            kodeCust: 0,
            namaCust: '',
            alamatCust: '',
            tempatCust: ''
        },
        consignee: {
            kodeTerima: 0,
            namaCons: '',
            alamatCons: '',
            kotaCons: ''
        },
        agent: {
            kodeAgent: 0,
            namaAgent: '',
            alamatAgent: ''
        },
        kotaAsal: {
            kodeAsal: 0,
            kotaAsal: ''
        },
        kotaTujuan: {
            kodeTujuan: 0,
            kotaTujuan: ''
        },
        shippingComp: {
            kodeLine: 0,
            namaLine: ''
        },
        jadwal: {
            kodeJadwal: 0
        },
        vessel: '',
        voyage: '',
        etd: '',
        eta: '',
        tglDooring: '',
        noContainer: '',
        noSeal: '',
        container: {
            qtyContainer: '',
            namaContainer: ''
        },
        komoditi: '',
        unit: '',
        kondisi: '',
        ket: '',
        user: 0
    });

    const [userId, setUserId] = useState(0);
    const [nomor, setNomor] = useState({
        fid: 0,
        fnomor: 0,
        ftempno: 0
    });

    const toast = useRef<any>(null);

    const showToast = (severity: string, summary: string, detail: string) => {
        toast.current.show({ severity, summary, detail, life: 3000 });
    };

    useEffect(() => {
        const fetchAndUpdateNomor = async () => {
            try {
                const res = await getNomor({ nama: 'ORDER' });

                setNomor(res.data[0]);
            } catch (error) {
                console.error('Error fetching nomor:', error);
            }
        };

        fetchAndUpdateNomor();
    }, []);

    useEffect(() => {
        const storedUser = localStorage.getItem('user');

        if (storedUser) {
            setUserId(JSON.parse(storedUser).id || 0);
            setOrder((prevOrder) => ({ ...prevOrder, user: JSON.parse(storedUser).id || 0 }));
        }
    }, []);

    useEffect(() => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            kodeOrder: `${nomor.ftempno}-${moment().format('MM.YYYY')}`
        }));
    }, [nomor]);

    const handleInputChange = (key: any, value: any) => setOrder({ ...order, [key]: value });

    const [selectedKotaType, setSelectedKotaType] = useState<'asal' | 'tujuan'>('asal');

    const [containers, setContainers] = useState([]);

    const loadContainers = useCallback(async () => {
        try {
            const res = await getContainers();

            setContainers(res.data);
        } catch (error) {
            showToast('error', 'Error', 'Failed to fetch containers');
            console.error('Error fetching containers:', error);
        }
    }, []);

    useEffect(() => {
        loadContainers();
    }, [loadContainers]);

    const onSalesGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getSales(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch sales');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onTrukGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getTruk(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch truk');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onCustomerGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getCustomers(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch customers');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onConsigneeGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getConsignees(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch consignees');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onAgentGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getAgents(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch agents');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onKotaGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getKota(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch kotas');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onShippingGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getShippingComps(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch kotas');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const onJadwalGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await getJadwal(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.log('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch jadwals');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const salesOP = useRef(null);
    const trukOP = useRef(null);
    const customerOP = useRef(null);
    const consigneeOP = useRef(null);
    const agentOP = useRef(null);
    const kotaOP = useRef(null);
    const shippingCompOP = useRef(null);
    const jadwalOP = useRef(null);

    const onSelectedSales = (sales: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            sales: {
                kodeSales: sales.data.fKodeSales ?? 0,
                namaSales: sales.data.fNama ?? ''
            }
        }));

        (salesOP.current as any)?.hide();
    };

    const onSelectedTruk = (truk: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            truk: { kodeTruk: truk.data.fKodeTruk ?? 0 },
            trukComp: { kodeComp: truk.data.fKodeComp ?? 0 },
            platTruk: truk.data.fPlat ?? '',
            supir: truk.data.fSupir ?? ''
        }));

        (trukOP.current as any)?.hide();
    };

    const onSelectedCustomer = (customer: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            shipper: {
                kodeCust: customer.data.fKodeCust ?? 0,
                namaCust: customer.data.fNama ?? '',
                alamatCust: customer.data.fAlamat ?? '',
                tempatCust: customer.data.fTempat ?? ''
            },
            tmptBongkar: customer.data.fTempat ?? ''
        }));

        (customerOP.current as any)?.hide();
    };

    const onSelectedConsignee = (consignee: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            consignee: {
                kodeTerima: consignee.data.fKodeTerima ?? 0,
                namaCons: consignee.data.fNama ?? '',
                alamatCons: consignee.data.fAlamat ?? '',
                kotaCons: consignee.data.fKota ?? ''
            }
        }));

        (consigneeOP.current as any)?.hide();
    };

    const onSelectedAgent = (agent: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            agent: {
                kodeAgent: agent.data.fAgent ?? 0,
                namaAgent: agent.data.fNama ?? '',
                alamatAgent: agent.data.fAlamat ?? ''
            }
        }));

        (agentOP.current as any)?.hide();
    };

    const onSelectedKota = (kota: { data: any }) => {
        if (selectedKotaType === 'asal') {
            setOrder((prevOrder) => ({
                ...prevOrder,
                kotaAsal: {
                    kodeAsal: kota.data.fKodeKota ?? 0,
                    kotaAsal: kota.data.fNama ?? ''
                }
            }));
        } else if (selectedKotaType === 'tujuan') {
            setOrder((prevOrder) => ({
                ...prevOrder,
                kotaTujuan: {
                    kodeTujuan: kota.data.fKodeKota ?? 0,
                    kotaTujuan: kota.data.fNama ?? ''
                }
            }));
        }

        (kotaOP.current as any)?.hide();
    };

    const onSelectedShippingComp = (shippingComp: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            shippingComp: {
                kodeLine: shippingComp.data.fKodeLine ?? 0,
                namaLine: shippingComp.data.fNama ?? ''
            }
        }));

        (shippingCompOP.current as any)?.hide();
    };

    const onSelectedJadwal = (jadwal: { data: any }) => {
        setOrder((prevOrder) => ({
            ...prevOrder,
            jadwal: {
                kodeJadwal: jadwal.data.fkodejadwal ?? 0
            },
            vessel: jadwal.data.fvessel,
            voyage: jadwal.data.fvoyage,
            eta: jadwal.data.feta,
            etd: jadwal.data.fetd
        }));

        (jadwalOP.current as any)?.hide();
    };

    const onSelectedContainer = (e: DropdownChangeEvent) => {
        const selected = containers.find((c: any) => c.fNama === e.value);
        setOrder((prev) => ({
            ...prev,
            container: {
                qtyContainer: (selected as any)?.fQtyM ?? 0,
                namaContainer: (selected as any)?.fNama ?? ''
            }
        }));
    };

    const handleSubmit = async () => {
        try {
            const cleanedDetails = orderDetails.filter((d) => d.namaBarang || d.jumlah || d.satuan || d.keterangan);

            const res = await createOrder({ master: order, details: cleanedDetails, nomor });
            if (res.success) {
                toast.current?.show({ severity: 'success', summary: 'Success', detail: res.message, life: 3000 });
                onClose();
            } else {
                toast.current?.show({ severity: 'error', summary: 'Error', detail: res.message, life: 3000 });
            }
        } catch (error) {
            console.error('Error creating order:', error);
            toast.current?.show({ severity: 'error', summary: 'Error', detail: 'Error creating order', life: 3000 });
        }
    };

    const [orderDetails, setOrderDetails] = useState([{ namaBarang: '', jumlah: 0, satuan: '', keterangan: '' }]);

    const detailColumns = [
        {
            headerName: 'Nama Barang',
            field: 'namaBarang'
        },
        {
            headerName: 'Jumlah',
            field: 'jumlah',
            cellEditor: 'agNumberCellEditor'
        },
        {
            headerName: 'Satuan',
            field: 'satuan'
        },
        {
            headerName: 'Keterangan',
            field: 'keterangan'
        },
        {
            headerName: '',
            field: 'actions',
            cellRenderer: (params: { node: { rowIndex: any } }) => {
                const isLastRow = params.node.rowIndex === orderDetails.length - 1;
                const canDelete = orderDetails.length > 1;

                return (
                    <div className="flex gap-2">
                        {canDelete && <Button icon="pi pi-trash" severity="danger" size="small" onClick={() => handleRemoveDetail(params.node.rowIndex)} />}
                        {isLastRow && <Button icon="pi pi-plus" severity="success" size="small" onClick={handleAddDetail} />}
                    </div>
                );
            },
            width: 50,
            editable: false
        }
    ];

    const handleAddDetail = () => {
        setOrderDetails((prev) => [...prev, { namaBarang: '', jumlah: 0, satuan: '', keterangan: '' }]);
    };

    const handleRemoveDetail = (index: number) => {
        setOrderDetails((prev) => prev.filter((_, i) => i !== index));
    };

    return (
        <React.Fragment>
            <Toast ref={toast} />
            <h1 className="text-xl font-bold">Input Order</h1>
            <div className="flex gap-2 mb-3">
                <Button label="Create" icon="pi pi-plus" severity="info" size="small" disabled />
                <Button label="Save" icon="pi pi-save" severity="success" size="small" onClick={handleSubmit} />
                <Button label="Edit" icon="pi pi-pencil" severity="warning" size="small" disabled />
                <Button label="Delete" icon="pi pi-trash" severity="danger" size="small" disabled />
                <Button label="Cancel" icon="pi pi-times" severity="secondary" size="small" onClick={onClose} />
            </div>
            <div className="grid">
                <div className="col-12 md:col-4">
                    <Panel header="Info Order" className="mb-3">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="kodeOrder" className="text-xs w-5rem">
                                    Kode Order
                                </label>
                                <InputText id="kodeOrder" className="w-full p-inputtext-sm" size="small" value={order.kodeOrder} readOnly />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="tgl" className="text-xs w-5rem">
                                    Tanggal Order
                                </label>
                                <Calendar id="tgl" className="w-full p-inputtext-sm" value={order.tgl ? new Date(order.tgl) : null} onChange={(e) => handleInputChange('tgl', e.value)} required dateFormat="dd-mm-yy" />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="sales" className="text-xs w-5rem">
                                    Sales
                                </label>
                                <InputText id="sales" className="w-full p-inputtext-sm" size="small" onClick={(e) => (salesOP.current as any)?.toggle(e)} value={order.sales.namaSales} required />
                                <OverlayPanel ref={salesOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={[{ headerName: 'Name', field: 'fNama' }] as any}
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onSalesGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedSales}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                        </div>
                    </Panel>
                    <Panel header="Info Transport">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="truk" className="text-xs w-5rem">
                                    Truk
                                </label>
                                <InputText id="truk" className="w-full p-inputtext-sm" size="small" onClick={(e) => (trukOP.current as any)?.toggle(e)} value={order.platTruk} required />
                                <OverlayPanel ref={trukOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Kode Comp', field: 'fKodeComp' },
                                                    { headerName: 'Plat Truk', field: 'fPlat' },
                                                    { headerName: 'Supir', field: 'fSupir' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            onGridReady={onTrukGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedTruk}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="sjNo" className="text-xs w-5rem">
                                    No SJ
                                </label>
                                <InputText id="sjNo" className="w-full p-inputtext-sm" size="small" required value={order.sjNo} onChange={(e) => handleInputChange('sjNo', e.target.value)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="platTruk" className="text-xs w-5rem">
                                    Plat Mobil
                                </label>
                                <InputText id="platTruk" className="w-full p-inputtext-sm" size="small" readOnly value={order.platTruk} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="supir" className="text-xs w-5rem">
                                    Supir
                                </label>
                                <InputText id="supir" className="w-full p-inputtext-sm" size="small" readOnly value={order.supir} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="sheet" className="text-xs w-5rem">
                                    Sheet
                                </label>
                                <InputText id="sheet" className="w-full p-inputtext-sm" size="small" required value={order.sheet} onChange={(e) => handleInputChange('sheet', e.target.value)} />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="ket" className="text-xs w-5rem">
                                    Keterangan
                                </label>
                                <InputTextarea id="ket" className="w-full p-inputtext-sm" rows={2} value={order.ket} onChange={(e) => handleInputChange('ket', e.target.value)} autoComplete="off" />
                            </div>
                        </div>
                    </Panel>
                </div>
                <div className="col-12 md:col-4">
                    <Panel header="Info Contact">
                        <div className="grid formgrid">
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="shipper" className="text-xs w-5rem">
                                    Shipper
                                </label>
                                <InputText id="shipper" className="w-full p-inputtext-sm" size="small" required value={order.shipper.namaCust} onClick={(e) => (customerOP.current as any)?.toggle(e)} />
                                <OverlayPanel ref={customerOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' },
                                                    { headerName: 'Kota', field: 'fKota' },
                                                    { headerName: 'Tempat', field: 'fTempat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onCustomerGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedCustomer}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="shipperAddress" className="text-xs w-5rem">
                                    Address Shipper
                                </label>
                                <InputText id="shipperAddress" className="w-full p-inputtext-sm" size="small" value={order.shipper.alamatCust} readOnly />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="tmptBongkar" className="text-xs w-5rem">
                                    Tempat Ambil
                                </label>
                                <InputText id="tmptBongkar" className="w-full p-inputtext-sm" size="small" value={order.shipper.tempatCust} readOnly />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="consignee" className="text-xs w-5rem">
                                    Consignee
                                </label>
                                <InputText id="consignee" className="w-full p-inputtext-sm" size="small" required value={order.consignee.namaCons} onClick={(e) => (consigneeOP.current as any)?.toggle(e)} />
                                <OverlayPanel ref={consigneeOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' },
                                                    { headerName: 'Kota', field: 'fKota' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onConsigneeGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedConsignee}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="consigneeAddress" className="text-xs w-5rem">
                                    Address Consignee
                                </label>
                                <InputText id="consigneeAddress" className="w-full p-inputtext-sm" size="small" value={order.consignee.alamatCons} readOnly />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="kotaTerima" className="text-xs w-5rem">
                                    Kota Tujuan
                                </label>
                                <InputText id="kotaTerima" className="w-full p-inputtext-sm" size="small" value={order.consignee.kotaCons} readOnly />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="agent" className="text-xs w-5rem">
                                    Agent
                                </label>
                                <InputText id="agent" className="w-full p-inputtext-sm" size="small" required value={order.agent.namaAgent} onClick={(e) => (agentOP.current as any)?.toggle(e)} />
                                <OverlayPanel ref={agentOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onAgentGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedAgent}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="addressAgent" className="text-xs w-5rem">
                                    Address Agent
                                </label>
                                <InputText id="addressAgent" className="w-full p-inputtext-sm" size="small" value={order.agent.alamatAgent} readOnly />
                            </div>
                        </div>
                    </Panel>
                </div>
                <div className="col-12 md:col-4">
                    <Panel header="Info Shipment">
                        <div className="grid formgrid">
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="portAsal" className="text-xs w-5rem">
                                    Port Asal
                                </label>
                                <InputText
                                    id="portAsal"
                                    className="w-full p-inputtext-sm"
                                    size="small"
                                    required
                                    value={order.kotaAsal.kotaAsal}
                                    onClick={(e) => {
                                        setSelectedKotaType('asal');
                                        (kotaOP.current as any)?.toggle(e);
                                    }}
                                />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="portTujuan" className="text-xs w-5rem">
                                    Port Tujuan
                                </label>
                                <InputText
                                    id="portTujuan"
                                    className="w-full p-inputtext-sm"
                                    size="small"
                                    required
                                    value={order.kotaTujuan.kotaTujuan}
                                    onClick={(e) => {
                                        setSelectedKotaType('tujuan');
                                        (kotaOP.current as any)?.toggle(e);
                                    }}
                                />
                            </div>
                            <OverlayPanel ref={kotaOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                    <AgGridReact
                                        columnDefs={[{ headerName: 'Nama', field: 'fNama' }] as any}
                                        rowHeight={30}
                                        rowModelType="infinite"
                                        cacheBlockSize={50}
                                        maxBlocksInCache={5}
                                        rowBuffer={0}
                                        onGridReady={onKotaGridReady}
                                        defaultColDef={{ sortable: true, flex: 1, resizable: false, lockPosition: true }}
                                        onRowDoubleClicked={(e) => onSelectedKota(e)}
                                    />
                                </div>
                            </OverlayPanel>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="shipComp" className="text-xs w-5rem">
                                    Shipping Comp
                                </label>
                                <InputText id="shipComp" className="w-full p-inputtext-sm" size="small" required value={order.shippingComp.namaLine} onClick={(e) => (shippingCompOP.current as any)?.toggle(e)} />
                                <OverlayPanel ref={shippingCompOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }}>
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Nama', field: 'fNama' },
                                                    { headerName: 'Alamat', field: 'fAlamat' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onShippingGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedShippingComp}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="vessel" className="text-xs w-5rem">
                                    Vessel
                                </label>
                                <InputText id="vessel" className="w-full p-inputtext-sm" size="small" required value={order.vessel} onClick={(e) => (jadwalOP.current as any)?.toggle(e)} />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="voyage" className="text-xs w-5rem">
                                    Voyage
                                </label>
                                <InputText id="voyage" className="w-full p-inputtext-sm" size="small" required value={order.voyage} readOnly />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="eta" className="text-xs w-5rem">
                                    ETA
                                </label>
                                <Calendar id="eta" className="w-full p-inputtext-sm" required value={order.eta ? new Date(order.eta) : null} readOnlyInput dateFormat="dd-mm-yy" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="etd" className="text-xs w-5rem">
                                    ETD
                                </label>
                                <Calendar id="etd" className="w-full p-inputtext-sm" required value={order.etd ? new Date(order.etd) : null} readOnlyInput dateFormat="dd-mm-yy" />
                                <OverlayPanel ref={jadwalOP} closeOnEscape dismissable={false} className="w-full" style={{ minWidth: '300px', maxWidth: '600px' }}>
                                    <div className="ag-theme-alpine" style={{ height: '400px', width: '100%' }} id="jadwal">
                                        <AgGridReact
                                            columnDefs={
                                                [
                                                    { headerName: 'Vessel', field: 'fvessel' },
                                                    { headerName: 'Voyage', field: 'fvoyage' },
                                                    { headerName: 'ETA', field: 'feta' },
                                                    { headerName: 'ETD', field: 'fetd' }
                                                ] as any
                                            }
                                            rowHeight={30}
                                            rowModelType="infinite"
                                            cacheBlockSize={50}
                                            maxBlocksInCache={5}
                                            rowBuffer={0}
                                            onGridReady={onJadwalGridReady}
                                            rowSelection={'single'}
                                            defaultColDef={{ sortable: true, flex: 1, resizable: false, filter: 'agTextColumnFilter', floatingFilter: true }}
                                            onRowDoubleClicked={onSelectedJadwal}
                                        />
                                    </div>
                                </OverlayPanel>
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="tglDooring" className="text-xs w-5rem">
                                    Tanggal Dooring
                                </label>
                                <Calendar id="tglDooring" className="w-full p-inputtext-sm" value={order.tglDooring ? new Date(order.tglDooring) : null} onChange={(e) => handleInputChange('tglDooring', e.value)} required dateFormat="dd-mm-yy" />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="kondisi" className="text-xs w-5rem">
                                    Kondisi
                                </label>
                                <Dropdown
                                    id="kondisi"
                                    className="w-full p-inputtext-sm"
                                    value={order.kondisi}
                                    onChange={(e) => handleInputChange('kondisi', e.value)}
                                    required
                                    options={['PORT TO PORT', 'PORT TO DOOR', 'PORT TO CY', 'DOOR TO PORT', 'DOOR TO DOOR', 'DOOR TO CY', 'CY TO PORT', 'CY TO DOOR', 'CY TO CY']}
                                />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="noContainer" className="text-xs w-5rem">
                                    No. Container
                                </label>
                                <InputText id="noContainer" className="w-full p-inputtext-sm" size="small" value={order.noContainer} onChange={(e) => handleInputChange('noContainer', e.target.value)} required />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="noSeal" className="text-xs w-5rem">
                                    No. Seal
                                </label>
                                <InputText id="noSeal" className="w-full p-inputtext-sm" size="small" value={order.noSeal} onChange={(e) => handleInputChange('noSeal', e.target.value)} required />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="ukuran" className="text-xs w-5rem">
                                    Ukuran
                                </label>
                                <Dropdown
                                    id="ukuran"
                                    className="w-full p-inputtext-sm"
                                    required
                                    value={order.container.namaContainer}
                                    options={containers}
                                    onChange={onSelectedContainer}
                                    optionLabel="fNama"
                                    optionValue="fNama"
                                    filter
                                    itemTemplate={(container) => (
                                        <div className="flex align-items-center">
                                            <div>{container.fNama}</div>
                                        </div>
                                    )}
                                />
                            </div>
                            <div className="field col-12 md:col-6 flex align-items-center gap-1 mb-1">
                                <label htmlFor="unit" className="text-xs w-5rem">
                                    Unit
                                </label>
                                <InputText id="unit" className="w-full p-inputtext-sm" size="small" value={order.unit} onChange={(e) => handleInputChange('unit', e.target.value)} required />
                            </div>
                            <div className="field col-12 flex align-items-center gap-1 mb-1">
                                <label htmlFor="komoditi" className="text-xs w-5rem">
                                    Komoditi
                                </label>
                                <InputText id="komoditi" className="w-full p-inputtext-sm" size="small" value={order.komoditi} onChange={(e) => handleInputChange('komoditi', e.target.value)} required />
                            </div>
                        </div>
                    </Panel>
                </div>
                <div className="col-12">
                    <h2 className="text-lg font-bold">Order Details</h2>
                    <div className="ag-theme-alpine" style={{ height: 300, width: '100%' }}>
                        <AgGridReact
                            rowData={orderDetails}
                            columnDefs={detailColumns as any}
                            defaultColDef={{
                                sortable: true,
                                editable: true,
                                resizable: false,
                                flex: 1
                            }}
                            onCellValueChanged={(params) => {
                                const field = params.colDef.field;
                                if (!field || typeof params.node.rowIndex !== 'number') return;

                                const newData = [...orderDetails];
                                newData[params.node.rowIndex] = {
                                    ...newData[params.node.rowIndex],
                                    [field]: params.newValue
                                };

                                const lastRow = newData[newData.length - 1];
                                const isLastRowFilled = lastRow.namaBarang || lastRow.jumlah || lastRow.satuan || lastRow.keterangan;

                                if (params.node.rowIndex === newData.length - 1 && isLastRowFilled) {
                                    newData.push({ namaBarang: '', jumlah: 0, satuan: '', keterangan: '' });
                                }

                                setOrderDetails(newData);
                            }}
                            stopEditingWhenCellsLoseFocus={true}
                            suppressClickEdit={false}
                        />
                    </div>
                </div>
            </div>
        </React.Fragment>
    );
};

export default CreateOrder;
