'use client';

import { fetchOrders } from '@/services/orderService';
import type { GridReadyEvent, RowDoubleClickedEvent } from 'ag-grid-community';
import { DateFilterModule, InfiniteRowModelModule, ModuleRegistry, NumberFilterModule, RowSelectionModule, TextFilterModule } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import moment from 'moment';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Card } from 'primereact/card';
import { Checkbox } from 'primereact/checkbox';
import { TabMenu } from 'primereact/tabmenu';
import { Toast } from 'primereact/toast';
import React, { useMemo, useRef, useState } from 'react';
import CreateOrder from './create';
import EditOrder from './edit';

ModuleRegistry.registerModules([NumberFilterModule, RowSelectionModule, InfiniteRowModelModule, DateFilterModule, TextFilterModule]);

const OrdersPage = () => {
    const [tab, setTab] = useState(0);
    const handleTabChange = (e: { index: number }) => setTab(e.index);

    const [editId, setEditId] = useState('');

    const [totalItems, setTotalItems] = useState(0);

    const [columnDefs] = useState([
        {
            headerName: 'No.',
            valueGetter: (params: { node: { rowIndex: number } }) => params.node.rowIndex + 1,
            sortable: false,
            filter: false
        },
        {
            headerName: 'Kode Order',
            field: 'kodeOrder'
        },
        {
            headerName: 'Tgl Order',
            field: 'tgl',
            filter: 'agDateColumnFilter',
            valueFormatter: ({ value }: { value: string | null }) => (value ? moment(value).format('DD-MM-YYYY') : '')
        },
        {
            headerName: 'Tempat Ambil',
            field: 'tempatCust'
        },
        {
            headerName: 'Tempat Bongkar',
            field: 'tmptBongkar'
        },
        {
            headerName: 'Plat Mobil',
            field: 'platTruk'
        },
        {
            headerName: 'Supir',
            field: 'supir'
        },
        {
            headerName: 'Komoditi',
            field: 'komoditi'
        },
        {
            headerName: 'Ukuran',
            field: 'namaContainer'
        },
        {
            headerName: 'No. Container',
            field: 'noContainer'
        },
        {
            headerName: 'No. Seal',
            field: 'noSeal'
        },
        {
            headerName: 'Load Sheet',
            field: 'sheet'
        },
        {
            headerName: 'Shipper',
            field: 'namaCust'
        },
        {
            headerName: 'Consignee',
            field: 'namaCons'
        },
        {
            headerName: 'Kota Tujuan',
            field: 'kotaTujuan'
        },
        {
            headerName: 'Agent',
            field: 'namaAgent'
        },
        {
            headerName: 'Vessel',
            field: 'vessel'
        },
        {
            headerName: 'Voyage',
            field: 'voyage'
        },
        {
            headerName: 'Port Asal',
            field: 'kotaAsal'
        },
        {
            headerName: 'Port Tujuan',
            field: 'kotaCons'
        },
        {
            headerName: 'Tgl. TD',
            field: 'etd',
            filter: 'agDateColumnFilter',
            valueFormatter: ({ value }: { value: string | null }) => (value ? moment(value).format('DD-MM-YYYY') : '')
        },
        {
            headerName: 'Tgl. TA',
            field: 'eta',
            filter: 'agDateColumnFilter',
            valueFormatter: ({ value }: { value: string | null }) => (value ? moment(value).format('DD-MM-YYYY') : '')
        },
        {
            headerName: 'Tgl. Dooring',
            field: 'tglDooring',
            filter: 'agDateColumnFilter',
            valueFormatter: ({ value }: { value: string | null }) => (value ? moment(value).format('DD-MM-YYYY') : '')
        },
        {
            headerName: 'Keterangan',
            field: 'ket'
        },
        {
            headerName: 'Shipping Comp',
            field: 'namaLine'
        },
        {
            headerName: 'No. SJ',
            field: 'sjNo'
        }
    ]);

    const toast = useRef<any>(null);

    const showToast = (severity: string, summary: string, detail: string) => {
        toast.current.show({ severity, summary, detail, life: 3000 });
    };

    const onGridReady = async (params: GridReadyEvent) => {
        const dataSource = {
            getRows: async (params: any) => {
                const { startRow, endRow, filterModel, sortModel } = params;

                try {
                    const res = await fetchOrders(startRow, endRow, filterModel, sortModel);
                    const rows = res.data;
                    const lastRow = res.total <= endRow ? res.total : -1;

                    setTotalItems(res.total);
                    params.successCallback(rows, lastRow);
                } catch (error) {
                    console.error('Error fetching filtered rows', error);
                    params.failCallback();
                    showToast('error', 'Error', 'Failed to fetch orders');
                }
            }
        };

        params.api.setGridOption('datasource', dataSource);
    };

    const defaultColDef = useMemo(() => {
        return {
            sortable: true,
            resizable: false,
            filter: 'agTextColumnFilter',
            floatingFilter: true
        };
    }, []);

    const items = useMemo(() => {
        if (editId !== '') {
            return [
                { label: 'Edit Order', icon: 'pi pi-pencil', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        } else {
            return [
                { label: 'Input Order', icon: 'pi pi-plus', command: () => setTab(0) },
                { label: 'Browse', icon: 'pi pi-list', command: () => setTab(1) }
            ];
        }
    }, [editId]);

    const onRowDoubleClicked = (params: RowDoubleClickedEvent) => {
        setEditId(params.data.kodeOrder);
        setTab(0);
    };

    const gridRef = useRef<any>(null);
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);
    const [useDateRange, setUseDateRange] = useState(false);

    const applyDateRangeToGrid = () => {
        if (!gridRef.current) return;

        const api = gridRef.current.api;
        const fm = { ...(api.getFilterModel?.() ?? {}) };

        if (!useDateRange) {
            delete fm.tgl;

            api.setFilterModel(fm);

            return;
        }

        const hasStart = !!startDate;
        const hasEnd = !!endDate;

        if (!hasStart && !hasEnd) {
            delete fm.tgl;
        } else if (hasStart && hasEnd) {
            fm.tgl = {
                filterType: 'date',
                type: 'inRange',
                dateFrom: moment(startDate).format('YYYY-MM-DD'),
                dateTo: moment(endDate).format('YYYY-MM-DD')
            };
        } else if (hasStart) {
            fm.tgl = {
                filterType: 'date',
                type: 'greaterThan',
                dateFrom: moment(startDate).format('YYYY-MM-DD')
            };
        } else {
            fm.tgl = {
                filterType: 'date',
                type: 'lessThan',
                dateTo: moment(endDate).format('YYYY-MM-DD')
            };
        }

        api.setFilterModel(fm);
    };

    const handleExportAll = async () => {
        try {
            const api = gridRef.current?.api;
            const filterModel = api?.getFilterModel?.() ?? {};
            const sortModel = api?.getSortModel?.() ?? [];

            const res = await fetchOrders(0, totalItems, filterModel, sortModel);
            const rows = res.data ?? [];

            const headers = (columnDefs as any[]).map((c) => `"${c.headerName ?? ''}"`).join(',');

            const lines = rows.map((row: any) =>
                (columnDefs as any[])
                    .map((col) => {
                        const field = col.field as string | undefined;
                        let val = field ? row[field] : '';

                        if (col.valueFormatter && field) {
                            try {
                                val = col.valueFormatter({ value: row[field] });
                            } catch {
                                /* ignore */
                            }
                        }

                        const s = (val ?? '').toString().replace(/"/g, '""');

                        return `"${s}"`;
                    })
                    .join(',')
            );

            const csvContent = [headers, ...lines].join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');

            a.href = url;
            a.download = 'orders_export.csv';
            a.click();

            URL.revokeObjectURL(url);
        } catch (err) {
            showToast('error', 'Export Failed', 'Gagal export data dari server');
        }
    };

    return (
        <React.Fragment>
            <Toast ref={toast} />
            <Card>
                <TabMenu model={items} activeIndex={tab} onTabChange={handleTabChange} className="-mt-5" />
                {editId === '' && tab === 0 && <CreateOrder onClose={() => setTab(1)} />}
                {editId !== '' && tab === 0 && (
                    <EditOrder
                        id={editId}
                        onClose={() => {
                            setEditId('');
                            setTab(1);
                        }}
                    />
                )}
                {tab === 1 && (
                    <>
                        <div className="row mt-3 -mb-4 flex align-items-center">
                            <div className="col-6">
                                <h1 className="text-xl font-bold -ml-2">Browse Orders</h1>
                            </div>
                            <div className="col-6 flex justify-content-end">
                                <h1 className="text-sm">Total: {totalItems}</h1>
                            </div>
                        </div>
                        <div className="row -mx-2 flex align-items-center">
                            <div className="col-8 flex gap-2 align-items-center">
                                <label htmlFor="startDate" className="text-sm">
                                    Tanggal
                                </label>
                                <Calendar id="startDate" value={startDate} onChange={(e) => setStartDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                <label htmlFor="endDate" className="text-sm">
                                    s/d
                                </label>
                                <Calendar value={endDate} onChange={(e) => setEndDate(e.value as Date | null)} dateFormat="dd-mm-yy" className="p-inputtext-sm" />
                                <Button
                                    label="Filter"
                                    icon="pi pi-filter"
                                    severity="success"
                                    size="small"
                                    className="p-button-sm"
                                    onClick={() => {
                                        setUseDateRange(true);
                                        applyDateRangeToGrid();
                                    }}
                                />
                                <Button
                                    label="Clear"
                                    icon="pi pi-times"
                                    severity="secondary"
                                    size="small"
                                    className="p-button-sm"
                                    onClick={() => {
                                        setUseDateRange(false);
                                        setStartDate(null);
                                        setEndDate(null);
                                        applyDateRangeToGrid();
                                    }}
                                />
                            </div>
                            <div className="col-4 flex justify-content-end">
                                <Button label="Export CSV" icon="pi pi-file" severity="info" size="small" onClick={handleExportAll} />
                            </div>
                        </div>
                        <div className="ag-theme-alpine -mb-3" style={{ height: 'calc(100vh - 338px)', width: '100%' }}>
                            <AgGridReact
                                ref={gridRef}
                                columnDefs={columnDefs as any}
                                defaultColDef={defaultColDef}
                                rowHeight={30}
                                rowModelType="infinite"
                                cacheBlockSize={100}
                                maxBlocksInCache={5}
                                rowBuffer={0}
                                onGridReady={onGridReady}
                                rowSelection="single"
                                multiSortKey="ctrl"
                                alwaysMultiSort={false}
                                onRowDoubleClicked={onRowDoubleClicked}
                                getRowId={(row: any) => row.kodeOrder}
                                getRowStyle={(params: any): { background: string } => {
                                    const isSelected = params.node.isSelected();
                                    const isEvenRow = params.node.rowIndex % 2 === 0;

                                    if (isSelected) {
                                        return { background: '#d0ebff' };
                                    } else if (!isEvenRow) {
                                        return { background: '#f9f9f9' };
                                    } else {
                                        return { background: '#ffffff' };
                                    }
                                }}
                            />
                        </div>
                    </>
                )}
            </Card>
        </React.Fragment>
    );
};

export default OrdersPage;
