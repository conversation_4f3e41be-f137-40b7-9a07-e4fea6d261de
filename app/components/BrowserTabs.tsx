'use client';

import '@/styles/layout/_tab.scss';
import { TabView, TabPanel } from 'primereact/tabview';
import { useTabContext } from '../../layout/context/tabcontext';
import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

export default function BrowserTabs() {
    const { tabs, activeTab, setActiveTab, closeTab } = useTabContext();
    const router = useRouter();
    const isUserClick = useRef(false);

    // Navigate when activeTab changes (e.g., after closing a tab)
    useEffect(() => {
        const current = tabs.find((t) => t.key === activeTab);
        if (current?.path) router.push(current.path);
    }, [activeTab, tabs, router]);

    return (
        <div className="browser-tabs-wrapper -mt-5 sticky-tabmenu">
            <TabView
                activeIndex={tabs.findIndex((t) => t.key === activeTab)}
                onTabChange={(e) => {
                    const tab = tabs[e.index];
                    if (tab) {
                        setActiveTab(tab.key);
                        if (tab.path) router.push(tab.path);
                    }
                }}
                scrollable
                lazy={false} // 👈 jangan unmount konten tab
                className="custom-tabview"
            >
                {tabs.map((tab) => (
                    <TabPanel
                        key={tab.key}
                        header={
                            <div className="tab-header">
                                <span>{tab.title}</span>
                                <i
                                    className="pi pi-times close-icon"
                                    onClick={(ev) => {
                                        ev.stopPropagation();
                                        closeTab(tab.key);
                                    }}
                                />
                            </div>
                        }
                    >
                        {/* 👇 Konten tab jangan kosong, render isi di sini */}
                        {tab.content}
                    </TabPanel>
                ))}
            </TabView>
        </div>
    );
}
