import db from '@/lib/db'

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const [data] = await db.query('SELECT * FROM tbcontainer ORDER BY fNama ASC')

            res.status(200).json({
                success: true,
                message: 'Containers fetched successfully',
                data
            })
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Error fetching containers',
                error: error.message
            })
        }
    } else {
        res.status(405).json({
            success: false,
            message: 'Method not allowed'
        })
    }

}

