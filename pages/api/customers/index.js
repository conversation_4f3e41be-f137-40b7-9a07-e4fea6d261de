import db from '@/lib/db';
import { buildOrderByClause, buildWhereClauseAndParams } from '../../../lib/queryUtils';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const { start = 0, end = 50, filter = '{}', sort = '[]' } = req.query;
            const limit = end - start;
            const offset = parseInt(start);
            const filterModel = JSON.parse(filter || '{}');
            const sortModel = JSON.parse(sort || '[]');

            const columnMap = {
                fNama: 'fNama',
                fAlamat: 'fAlamat',
                fKota: 'fKota',
                fTempat: 'fTempat'
            }

            const { whereClause, whereParams } = buildWhereClauseAndParams(filterModel, columnMap);
            const orderByClause = buildOrderByClause(sortModel, columnMap, 'ORDER BY fNama ASC');

            const [data] = await db.query(`SELECT * FROM tbcustomer ${whereClause} ${orderByClause} LIMIT ? OFFSET ?`, [...whereParams, limit, offset]);

            const [[{ total }]] = await db.query(`SELECT COUNT(*) as total FROM tbcustomer ${whereClause}`, [...whereParams]);

            res.status(200).json({
                success: true,
                message: 'Customers fetched successfully',
                data,
                total
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Error fetching customers',
                error: error.message
            });
        }
    } else if (req.method === 'POST') {
        const { nama, alamat, kota, tempat, telp, fax, email, attn, nomorH, user, kunci } = req.body;

        if (!nama || !alamat || !kota || !tempat || !telp || !fax || !email || !attn || !nomorH || !user || !kunci) {
            res.status(400).json({
                success: false,
                message: 'Error creating customer',
                error: 'Missing required fields'
            });
            return;
        }

        try {
            const [data] = await db.query('INSERT INTO tbcustomer (fNama, fAlamat, fKota, fTempat, fTelp, fFax, fEmail, fAttn, fNomorH, fUser, fKunci) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                nama,
                alamat,
                kota,
                tempat,
                telp,
                fax,
                email,
                attn,
                nomorH,
                user,
                kunci
            ]);

            res.status(201).json({
                success: true,
                message: 'Customer created successfully',
                data
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Error creating customer',
                error: error.message
            });
        }
    } else {
        res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
