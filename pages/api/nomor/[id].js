import db from '@/lib/db';

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        try {
            const { id } = req.query;
            const { fnomor, ftempno } = req.body;

            if (!id || !fnomor || !ftempno) {
                res.status(400).json({ success: false, message: 'Error updating nomor', error: 'Missing required fields' });
                return;
            }

            const [data] = await db.query('UPDATE tbnomor SET fnomor = ?, ftempno = ? WHERE fid = ?', [fnomor, ftempno, id]);

            res.status(200).json({ success: true, message: 'Nomor updated successfully', data });
        } catch (error) {
            res.status(500).json({ success: false, message: 'Error updating nomor', error: error.message });
        }
    } else {
        res.status(405).json({ success: false, message: 'Method not allowed' });
    }
}
