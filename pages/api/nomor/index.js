import db from '@/lib/db';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const { nama } = req.query

            const [data] = await db.query('SELECT * FROM tbnomor' + (nama ? ` WHERE fnama = "${nama}"` : '') + ' ORDER BY fnama ASC');

            res.status(200).json({
                success: true,
                message: 'Nomor fetched successfully',
                data
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Error fetching nomor',
                error: error.message
            });
        }
    } else {
        res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
