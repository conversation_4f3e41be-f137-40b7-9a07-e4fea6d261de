import db from '@/lib/db';
import { buildOrderByClause, buildWhereClauseAndParams } from '../../../lib/queryUtils';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const { start = 0, end = 50, filter = '{}', sort = '[]' } = req.query;
            const limit = end - start;
            const offset = parseInt(start);
            const filterModel = JSON.parse(filter || '{}');
            const sortModel = JSON.parse(sort || '[]');

            const columnMap = { fNama: 'fNama' };

            const { whereClause, whereParams } = buildWhereClauseAndParams(filterModel, columnMap);
            const orderByClause = buildOrderByClause(sortModel, columnMap, 'ORDER BY fNama ASC');

            const [data] = await db.query(`SELECT * FROM tbkota ${whereClause} ${orderByClause} LIMIT ? OFFSET ?`, [...whereParams, limit, offset]);

            const [[{ total }]] = await db.query(`SELECT COUNT(*) as total FROM tbkota ${whereClause}`, [whereParams]);

            res.status(200).json({
                success: true,
                message: 'Kota fetched successfully',
                data,
                total
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Error fetching kota',
                error: error.message
            });
        }
    } else {
        res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
