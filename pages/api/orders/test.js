// import { query } from '@/lib/db-2.js';
import { query } from '@/lib/db-2.js';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const rows = await query('SELECT * FROM tbagent'); // ganti nama tabel
            res.json({ ok: true, rows });
        } catch (e) {
            res.status(500).json({ ok: false, error: e?.message || String(e) });
        }
    }
}
