import db from '@/lib/db';
import moment from 'moment';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const { id } = req.query;

            const [[data]] = await db.query(
                `SELECT
                    tborder.fKodeOrder AS kodeOrder,
                    tborder.fSJNo AS sjNo,
                    tborder.fTgl AS tgl,
                    tborder.fTglDooring AS tglDooring,
                    tborder.fSupir AS supir,
                    tborder.fNoContainer AS noContainer,
                    tborder.fNoSeal AS noSeal,
                    tborder.fKomoditi AS komoditi,
                    tborder.fETD AS etd,
                    tborder.fETA AS eta,
                    tborder.fKet AS ket,
                    tborder.fsheet AS sheet,
                    tborder.fTmptBongkar AS tmptBongkar,
                    tborder.fUnit AS unit,
                    tborder.fCondition AS kondisi,
                    tbcustomer.fKodeCust AS kodeCust,
                    tbcustomer.fNama AS namaCust,
                    tbcustomer.fAlamat AS alamatCust,
                    tbcustomer.fTempat AS tempatCust,
                    tbcontainer.fQtyM AS qtyContainer,
                    tbcontainer.fNama AS namaContainer,
                    tbline.fKodeLine AS kodeLine,
                    tbline.fNama AS namaLine,
                    tbkota.fKodeKota AS kodeTujuan,
                    tbkota.fNama AS kotaTujuan,
                    asal.fKodeKota AS kodeAsal,
                    asal.fNama AS kotaAsal,
                    tbtruk.fPlat AS platTruk,
                    tbjadwalm.fVessel AS vessel,
                    tbjadwalm.fVoyage AS voyage,
                    tbterima.fKodeTerima AS kodeTerima,
                    tbterima.fNama AS namaCons,
                    tbterima.fAlamat AS alamatCons,
                    tbterima.fKota AS kotaCons,
                    tbagent.fAgent AS kodeAgent,
                    tbagent.fNama AS namaAgent,
                    tbagent.fAlamat AS alamatAgent,
                    tbsales.fKodeSales AS kodeSales,
                    tbsales.fNama AS namaSales,
                    tbtruk.fKodeTruk AS kodeTruk,
                    tbtruk.fKodeComp AS kodeComp,
                    tbtruk.fPlat AS platTruk,
                    tbtruk.fSupir AS supirTruk,
                    tbtrukcomp.fKodeComp AS kodeComp,
                    tbtrukcomp.fNama AS namaTrukComp,
                    tbjadwalm.fkodeJadwal AS kodeJadwal
                FROM
                    tborder
                    LEFT JOIN tbjadwalm ON tbjadwalm.fKodeJadwal = tborder.fKodeJadwal
                    LEFT JOIN tbline ON tbline.fKodeLine = tborder.fKodeLine
                    LEFT JOIN tbcustomer ON tbcustomer.fKodeCust = tborder.fKodeCust
                    LEFT JOIN tbcontainer ON tbcontainer.fQtyM = tborder.fQtyM
                    LEFT JOIN tbkota ON tbkota.fKodeKota = tborder.fTujuan
                    LEFT JOIN tbtruk ON tbtruk.fKodeTruk = tborder.fKodeTruk
                    LEFT JOIN tbkota asal ON asal.fKodeKota = tborder.fAsal
                    LEFT JOIN tbterima ON tbterima.fKodeTerima = tborder.fKodeTerima
                    LEFT JOIN tbagent ON tbagent.fAgent = tborder.fAgent
                    LEFT JOIN tbsales ON tbsales.fKodeSales = tborder.fKodeSales
                    LEFT JOIN tbtrukcomp ON tbtrukcomp.fKodeComp = tborder.fKodeComp
                WHERE tborder.fKodeOrder = ?`,
                [id]
            );

            if (!data) {
                return res.status(404).json({ success: false, message: 'Order not found' });
            }

            const [details] = await db.query(
                `SELECT
                    tborderdet.fKodeOrder AS kodeOrder,
                    tborderdet.fNamaBrg AS namaBarang,
                    tborderdet.fQty AS jumlah,
                    tborderdet.fUnit AS satuan,
                    tborderdet.fKet AS keterangan
                FROM tborderdet WHERE tborderdet.fKodeOrder = ?`,
                [id]
            );

            res.status(200).json({
                success: true,
                message: 'Order fetched successfully',
                data: {
                    ...data,
                    shipper: {
                        kodeCust: data.kodeCust,
                        namaCust: data.namaCust,
                        alamatCust: data.alamatCust,
                        tempatCust: data.tempatCust
                    },
                    container: {
                        qtyContainer: data.qtyContainer,
                        namaContainer: data.namaContainer
                    },
                    shippingComp: {
                        kodeLine: data.kodeLine,
                        namaLine: data.namaLine
                    },
                    kotaTujuan: {
                        kodeTujuan: data.kodeTujuan,
                        kotaTujuan: data.kotaTujuan
                    },
                    kotaAsal: {
                        kodeAsal: data.kodeAsal,
                        kotaAsal: data.kotaAsal
                    },
                    consignee: {
                        kodeTerima: data.kodeTerima,
                        namaCons: data.namaCons,
                        alamatCons: data.alamatCons,
                        kotaCons: data.kotaCons
                    },
                    agent: {
                        kodeAgent: data.kodeAgent,
                        namaAgent: data.namaAgent,
                        alamatAgent: data.alamatAgent
                    },
                    sales: {
                        kodeSales: data.kodeSales,
                        namaSales: data.namaSales
                    },
                    truk: {
                        kodeTruk: data.kodeTruk,
                        platTruk: data.platTruk,
                        supirTruk: data.supirTruk,
                        kodeComp: data.kodeComp,
                        trukConp: data.namaTrukComp
                    },
                    jadwal: {
                        kodeJadwal: data.kodeJadwal
                    },
                    details
                }
            });
        } catch (error) {
            res.status(500).json({ success: false, message: 'Error fetching order', error: error.message });
        }
    } else if (req.method === 'PUT') {
        const conn = await db.getConnection();

        try {
            await conn.beginTransaction();

            const { id } = req.query;
            const { master, details } = req.body;

            if (!id || !master || !details) {
                throw new Error('Missing required fields');
            }

            const {
                kodeOrder,
                sjNo,
                fGroup,
                tgl,
                tglDooring,
                truk,
                trukComp,
                kodeComp,
                supir,
                noContainer,
                noSeal,
                fBarang,
                container,
                sales,
                shippingComp,
                shipper,
                komoditi,
                kotaTujuan,
                agent,
                kondisi,
                jadwal,
                consignee,
                etd,
                eta,
                ket,
                user,
                fS1,
                fS2,
                sheet,
                kotaAsal,
                unit,
                tmptBongkar
            } = master;

            const [orderResult] = await conn.query(
                `
                UPDATE tborder SET
                    fSJNo = ?,
                    fGroup = ?,
                    fTgl = ?,
                    fTglDooring = ?,
                    fKodeTruk = ?,
                    fKodeComp = ?,
                    fSupir = ?,
                    fNoContainer = ?,
                    fNoSeal = ?,
                    fBarang = ?,
                    fQtyM = ?,
                    fKodeSales = ?,
                    fKodeLine = ?,
                    fKodeCust = ?,
                    fKomoditi = ?,
                    fTujuan = ?,
                    fAgent = ?,
                    fCondition = ?,
                    fKodeJadwal = ?,
                    fKodeTerima = ?,
                    fETD = ?,
                    fETA = ?,
                    fKet = ?,
                    fS1 = ?,
                    fS2 = ?,
                    fSheet = ?,
                    fKotaTerima = ?,
                    fAsal = ?,
                    fUnit = ?,
                    fTmptBongkar = ?
                WHERE fKodeOrder = ?`,
                [
                    sjNo,
                    fGroup,
                    moment(tgl).format('YYYY-MM-DD'),
                    moment(tglDooring).format('YYYY-MM-DD'),
                    truk.kodeTruk,
                    truk.kodeComp,
                    supir,
                    noContainer,
                    noSeal,
                    fBarang,
                    container.qtyContainer,
                    sales.kodeSales,
                    shippingComp.kodeLine,
                    shipper.kodeCust,
                    komoditi,
                    kotaTujuan.kodeTujuan,
                    agent.kodeAgent,
                    kondisi,
                    jadwal.kodeJadwal,
                    consignee.kodeTerima,
                    moment(etd).format('YYYY-MM-DD'),
                    moment(eta).format('YYYY-MM-DD'),
                    ket,
                    fS1,
                    fS2,
                    sheet,
                    consignee.kotaCons,
                    kotaAsal.kodeAsal,
                    unit,
                    tmptBongkar,
                    kodeOrder
                ]
            );

            if (orderResult.affectedRows === 0) {
                throw new Error('Error updating order');
            }

            await conn.query('DELETE FROM tborderdet WHERE fKodeOrder = ?', [kodeOrder]);

            for (const detail of details) {
                const { namaBarang, jumlah, satuan, keterangan } = detail;

                const [detailResult] = await conn.query('INSERT INTO tborderdet (fKodeOrder, fNamaBrg, fQty, fUnit, fKet) VALUES (?, ?, ?, ?, ?)', [kodeOrder, namaBarang, jumlah, satuan, keterangan]);

                if (detailResult.affectedRows === 0) {
                    throw new Error('Error updating order details');
                }
            }

            await conn.commit();

            res.status(200).json({ success: true, message: 'Order updated successfully', data: { master, details } });
        } catch (error) {
            await conn.rollback();

            res.status(500).json({ success: false, message: 'Error updating order', error: error.message });
        } finally {
            conn.release();
        }
    } else if (req.method === 'DELETE') {
        const conn = await db.getConnection();

        try {
            await conn.beginTransaction();

            const { id } = req.query;

            if (!id) {
                throw new Error('Missing required fields');
            }

            const [orderResult] = await conn.query('DELETE FROM tborder WHERE fKodeOrder = ?', [id]);

            if (orderResult.affectedRows === 0) {
                throw new Error('Error deleting order');
            }

            await conn.query('DELETE FROM tborderdet WHERE fKodeOrder = ?', [id]);

            await conn.commit();

            res.status(200).json({ success: true, message: 'Order deleted successfully' });
        } catch (error) {
            await conn.rollback();

            res.status(500).json({ success: false, message: 'Error deleting order', error: error.message });
        } finally {
            conn.release();
        }
    } else {
        res.status(405).json({ success: false, message: 'Method not allowed' });
    }
}
