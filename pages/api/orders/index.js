import db from '@/lib/db';
import moment from 'moment';
import { buildOrderByClause, buildWhereClauseAndParams } from '../../../lib/queryUtils';

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const { start = 0, end = 100, filter = '{}', sort = '[]' } = req.query;
            const limit = end - start;
            const offset = parseInt(start);
            const filterModel = JSON.parse(filter || '{}');
            const sortModel = JSON.parse(sort || '[]');

            const columnMap = {
                kodeOrder: 'tborder.fKodeOrder',
                tgl: 'tborder.fTgl',
                tempatCust: 'tbcustomer.fTempat',
                tmptBongkar: 'tborder.fTmptBongkar',
                platTruk: 'tbtruk.fPlat',
                supir: 'tborder.fSupir',
                komoditi: 'tborder.fKomoditi',
                namaContainer: 'tbcontainer.fNama',
                noContainer: 'tborder.fNoContainer',
                noSeal: 'tborder.fNoSeal',
                sheet: 'tborder.fSheet',
                namaCust: 'tbcustomer.fNama',
                namaCons: 'tbterima.fNama',
                kotaCons: 'tbterima.fKota',
                namaAgent: 'tbagent.fNama',
                vessel: 'tbjadwalm.fVessel',
                voyage: 'tbjadwalm.fVoyage',
                kotaAsal: 'tbkota.fNama',
                kotaTujuan: 'tbkota.fNama',
                etd: 'tborder.fETD',
                eta: 'tborder.fETA',
                tglDooring: 'tborder.fTglDooring',
                ket: 'tborder.fKet',
                namaLine: 'tbline.fNama',
                sjNo: 'tborder.fSJNo'
            };

            const { whereClause, whereParams } = buildWhereClauseAndParams(filterModel, columnMap);
            const orderByClause = buildOrderByClause(sortModel, columnMap, 'ORDER BY tborder.fTgl DESC, tborder.fKodeOrder ASC');

            const queryOrder = `SELECT
                tborder.fKodeOrder AS kodeOrder,
                tborder.fSJNo AS sjNo,
                tborder.fTgl AS tgl,
                tborder.fTglDooring AS tglDooring,
                tborder.fSupir AS supir,
                tborder.fNoContainer AS noContainer,
                tborder.fNoSeal AS noSeal,
                tborder.fKomoditi AS komoditi,
                tborder.fETD AS etd,
                tborder.fETA AS eta,
                tborder.fKet AS ket,
                tborder.fsheet AS sheet,
                tborder.fTmptBongkar AS tmptBongkar,
                tborder.fUnit AS unit,
                tborder.fCondition AS kondisi,
                tbcustomer.fKodeCust AS kodeCust,
                tbcustomer.fNama AS namaCust,
                tbcustomer.fTempat AS tempatCust,
                tbcontainer.fQtyM AS qtyContainer,
                tbcontainer.fNama AS namaContainer,
                tbline.fKodeLine AS kodeLine,
                tbline.fNama AS namaLine,
                tbkota.fKodeKota AS kodeTujuan,
                tbkota.fNama AS kotaTujuan,
                asal.fKodeKota AS kodeAsal,
                asal.fNama AS kotaAsal,
                tbtruk.fPlat AS platTruk,
                tbjadwalm.fVessel AS vessel,
                tbjadwalm.fVoyage AS voyage,
                tbterima.fKodeTerima AS kodeTerima,
                tbterima.fNama AS namaCons,
                tbterima.fKota AS kotaCons,
                tbagent.fAgent AS kodeAgent,
                tbagent.fNama AS namaAgent,
                tbsales.fKodeSales AS kodeSales,
                tbsales.fNama AS namaSales,
                tbtrukcomp.fKodeComp AS kodeComp,
                tbtrukcomp.fNama AS namaTrukComp
            FROM
                tborder
                LEFT JOIN tbjadwalm ON tbjadwalm.fKodeJadwal = tborder.fKodeJadwal
                LEFT JOIN tbline ON tbline.fKodeLine = tborder.fKodeLine
                LEFT JOIN tbcustomer ON tbcustomer.fKodeCust = tborder.fKodeCust
                LEFT JOIN tbcontainer ON tbcontainer.fQtyM = tborder.fQtyM
                LEFT JOIN tbkota ON tbkota.fKodeKota = tborder.fTujuan
                LEFT JOIN tbtruk ON tbtruk.fKodeTruk = tborder.fKodeTruk
                LEFT JOIN tbkota asal ON asal.fKodeKota = tborder.fAsal
                LEFT JOIN tbterima ON tbterima.fKodeTerima = tborder.fKodeTerima
                LEFT JOIN tbagent ON tbagent.fAgent = tborder.fAgent
                LEFT JOIN tbsales ON tbsales.fKodeSales = tborder.fKodeSales
                LEFT JOIN tbtrukcomp ON tbtrukcomp.fKodeComp = tborder.fKodeComp
            ${whereClause}
            ${orderByClause}
            LIMIT ? OFFSET ?`;

            const [data] = await db.query(queryOrder, [...whereParams, limit, offset]);

            const [[{ total }]] = await db.query(
                `SELECT COUNT(*) AS total
                FROM tborder
                LEFT JOIN tbjadwalm ON tbjadwalm.fKodeJadwal = tborder.fKodeJadwal
                LEFT JOIN tbline ON tbline.fKodeLine = tborder.fKodeLine
                LEFT JOIN tbcustomer ON tbcustomer.fKodeCust = tborder.fKodeCust
                LEFT JOIN tbcontainer ON tbcontainer.fQtyM = tborder.fQtyM
                LEFT JOIN tbkota ON tbkota.fKodeKota = tborder.fTujuan
                LEFT JOIN tbtruk ON tbtruk.fKodeTruk = tborder.fKodeTruk
                LEFT JOIN tbkota asal ON asal.fKodeKota = tborder.fAsal
                LEFT JOIN tbterima ON tbterima.fKodeTerima = tborder.fKodeTerima
                LEFT JOIN tbagent ON tbagent.fAgent = tborder.fAgent
                LEFT JOIN tbsales ON tbsales.fKodeSales = tborder.fKodeSales
                LEFT JOIN tbtrukcomp ON tbtrukcomp.fKodeComp = tborder.fKodeComp
                ${whereClause}`,
                [...whereParams]
            );

            res.status(200).json({
                success: true,
                message: 'Orders fetched successfully',
                data,
                total
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: 'Failed to fetch orders',
                error: error.message
            });
        }
    } else if (req.method === 'POST') {
        const conn = await db.getConnection();

        try {
            await conn.beginTransaction();

            const { master, details, nomor } = req.body;

            const { fid, ftempno } = nomor;
            let fnomor = (nomor.fnomor += 1);

            const [nomorResult] = await conn.query('UPDATE tbnomor SET fnomor = ?, ftempno = ? WHERE fid = ?', [fnomor, ftempno, fid]);

            if (nomorResult.affectedRows === 0) {
                throw new Error('Failed to update nomor');
            }

            const kodeOrder = `${fnomor}-${moment().format('MM.YYYY')}`;

            const {
                sjNo,
                fGroup,
                tgl,
                tglDooring,
                truk,
                trukComp,
                supir,
                noContainer,
                noSeal,
                fBarang,
                container,
                sales,
                shippingComp,
                shipper,
                komoditi,
                kotaTujuan,
                agent,
                kondisi,
                jadwal,
                consignee,
                etd,
                eta,
                ket,
                user,
                fS1,
                fS2,
                sheet,
                kotaAsal,
                unit,
                tmptBongkar
            } = master;

            const [orderResult] = await conn.query(
                `INSERT INTO tborder (
                    fKodeOrder,
                    fSJNo,
                    fGroup,
                    fTgl,
                    fTglDooring,
                    fKodeTruk,
                    fKodeComp,
                    fSupir,
                    fNoContainer,
                    fNoSeal,
                    fBarang,
                    fQtyM,
                    fKodeSales,
                    fKodeLine,
                    fKodeCust,
                    fKomoditi,
                    fTujuan,
                    fAgent,
                    fCondition,
                    fKodeJadwal,
                    fKodeTerima,
                    fETD,
                    fETA,
                    fKet,
                    fUser,
                    fS1,
                    fS2,
                    fSheet,
                    fKotaTerima,
                    fAsal,
                    fUnit,
                    fTmptBongkar
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    kodeOrder, // kodeOrder
                    sjNo, // sjNo
                    fGroup || null,
                    moment(tgl).format('YYYY-MM-DD'), // tgl
                    moment(tglDooring).format('YYYY-MM-DD'), // tglDooring
                    truk.kodeTruk, // kodeTruk
                    trukComp.kodeComp, // kodeTrukComp
                    supir, // supir
                    noContainer, // noContainer
                    noSeal, // noSeal
                    fBarang || null,
                    container.qtyContainer, // namaContainer
                    sales.kodeSales, // namaSales
                    shippingComp.kodeLine, // namaLine
                    shipper.kodeCust, // namaCust
                    komoditi, // komoditi
                    kotaTujuan.kodeTujuan, // kotaTujuan
                    agent.kodeAgent, // namaAgent
                    kondisi, // kondisi
                    jadwal.kodeJadwal, // kodeJadwal
                    consignee.kodeTerima, // namaCons
                    moment(etd).format('YYYY-MM-DD'), // etd
                    moment(eta).format('YYYY-MM-DD'), // eta
                    ket, // ket
                    user, // user
                    fS1 || null,
                    fS2 || null,
                    sheet, // sheet
                    consignee.kotaCons, // kotaCons
                    kotaAsal.kodeAsal, // kotaAsal
                    unit, // unit
                    tmptBongkar // tmptBongkar
                ]
            );

            if (orderResult.affectedRows === 0) {
                throw new Error('Failed to create order');
            }

            for (const detail of details) {
                const { namaBarang, jumlah, satuan, keterangan } = detail;

                const [detailResult] = await conn.query(
                    `INSERT INTO tborderdet (
                        fKodeOrder,
                        fNamaBrg,
                        fQty,
                        fUnit,
                        fKet
                    ) VALUES (?, ?, ?, ?, ?)`,
                    [kodeOrder, namaBarang, jumlah, satuan, keterangan]
                );

                if (detailResult.affectedRows === 0) {
                    throw new Error('Failed to create order detail');
                }
            }

            await conn.commit();

            res.status(200).json({
                success: true,
                message: 'Order created successfully',
                data: {
                    kodeOrder,
                    master,
                    details
                }
            });
        } catch (error) {
            await conn.rollback();

            res.status(500).json({
                success: false,
                message: 'Failed to create order',
                error: error.message
            });
        } finally {
            conn.release();
        }
    } else {
        res.status(405).json({
            success: false,
            message: 'Method not allowed'
        });
    }
}
