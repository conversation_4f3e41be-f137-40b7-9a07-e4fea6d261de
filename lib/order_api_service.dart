// lib/order_api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class Order {
  final String kodeOrder;
  final String sjNo;
  final String tgl;
  final String tglDooring;
  final String supir;
  final String noContainer;
  final String noSeal;
  final String komoditi;
  final String etd;
  final String eta;
  final String ket;
  final String sheet;
  final String tmptBongkar;
  final String unit;
  final String kondisi;
  final String kodeCust;
  final String namaCust;
  final String tempatCust;
  final int qtyContainer;
  final String namaContainer;
  final String kodeLine;
  final String namaLine;
  final String kodeTujuan;
  final String kotaTujuan;
  final String kodeAsal;
  final String kotaAsal;
  final String platTruk;
  final String vessel;
  final String voyage;
  final String kodeTerima;
  final String namaCons;
  final String kotaCons;
  final String kodeAgent;
  final String namaAgent;
  final String kodeSales;
  final String namaSales;
  final String kodeComp;
  final String namaTrukComp;

  Order({
    required this.kodeOrder,
    required this.sjNo,
    required this.tgl,
    required this.tglDooring,
    required this.supir,
    required this.noContainer,
    required this.noSeal,
    required this.komoditi,
    required this.etd,
    required this.eta,
    required this.ket,
    required this.sheet,
    required this.tmptBongkar,
    required this.unit,
    required this.kondisi,
    required this.kodeCust,
    required this.namaCust,
    required this.tempatCust,
    required this.qtyContainer,
    required this.namaContainer,
    required this.kodeLine,
    required this.namaLine,
    required this.kodeTujuan,
    required this.kotaTujuan,
    required this.kodeAsal,
    required this.kotaAsal,
    required this.platTruk,
    required this.vessel,
    required this.voyage,
    required this.kodeTerima,
    required this.namaCons,
    required this.kotaCons,
    required this.kodeAgent,
    required this.namaAgent,
    required this.kodeSales,
    required this.namaSales,
    required this.kodeComp,
    required this.namaTrukComp,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      kodeOrder: json['kodeOrder'] ?? '',
      sjNo: json['sjNo'] ?? '',
      tgl: json['tgl'] ?? '',
      tglDooring: json['tglDooring'] ?? '',
      supir: json['supir'] ?? '',
      noContainer: json['noContainer'] ?? '',
      noSeal: json['noSeal'] ?? '',
      komoditi: json['komoditi'] ?? '',
      etd: json['etd'] ?? '',
      eta: json['eta'] ?? '',
      ket: json['ket'] ?? '',
      sheet: json['sheet'] ?? '',
      tmptBongkar: json['tmptBongkar'] ?? '',
      unit: json['unit'] ?? '',
      kondisi: json['kondisi'] ?? '',
      kodeCust: json['kodeCust'] ?? '',
      namaCust: json['namaCust'] ?? '',
      tempatCust: json['tempatCust'] ?? '',
      qtyContainer: json['qtyContainer'] ?? 0,
      namaContainer: json['namaContainer'] ?? '',
      kodeLine: json['kodeLine'] ?? '',
      namaLine: json['namaLine'] ?? '',
      kodeTujuan: json['kodeTujuan'] ?? '',
      kotaTujuan: json['kotaTujuan'] ?? '',
      kodeAsal: json['kodeAsal'] ?? '',
      kotaAsal: json['kotaAsal'] ?? '',
      platTruk: json['platTruk'] ?? '',
      vessel: json['vessel'] ?? '',
      voyage: json['voyage'] ?? '',
      kodeTerima: json['kodeTerima'] ?? '',
      namaCons: json['namaCons'] ?? '',
      kotaCons: json['kotaCons'] ?? '',
      kodeAgent: json['kodeAgent'] ?? '',
      namaAgent: json['namaAgent'] ?? '',
      kodeSales: json['kodeSales'] ?? '',
      namaSales: json['namaSales'] ?? '',
      kodeComp: json['kodeComp'] ?? '',
      namaTrukComp: json['namaTrukComp'] ?? '',
    );
  }
}

class OrderApiService {
  final String baseUrl;

  OrderApiService({this.baseUrl = 'http://localhost:3000/api'}); // Sesuaikan dengan URL API Next.js Anda

  Future<List<Order>> fetchOrders({int start = 0, int end = 10, Map<String, dynamic>? filter, List<Map<String, dynamic>>? sort}) async {
    final Map<String, String> queryParams = {
      'start': start.toString(),
      'end': end.toString(),
    };

    if (filter != null) {
      queryParams['filter'] = jsonEncode(filter);
    }
    if (sort != null) {
      queryParams['sort'] = jsonEncode(sort);
    }

    final uri = Uri.parse('$baseUrl/orders').replace(queryParameters: queryParams);

    try {
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true) {
          final List<dynamic> orderJsonList = jsonResponse['data'];
          return orderJsonList.map((json) => Order.fromJson(json)).toList();
        } else {
          throw Exception('API returned an error: ${jsonResponse['message']}');
        }
      } else {
        throw Exception('Failed to load orders. Status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching orders: $e');
    }
  }
}