const moment = require('moment');

const buildWhereClauseAndParams = (filterModel, columnMap) => {
    let whereClause = 'WHERE 1=1';
    const whereParams = [];

    for (const field in filterModel) {
        const filter = filterModel[field];
        const dbColumn = columnMap[field];

        if (!dbColumn) continue;

        const filterType = filter.filterType;

        if (filterType === 'date') {
            const filterDate = moment(filter.dateFrom).format('YYYY-MM-DD');

            switch (filter.type) {
                case 'equals':
                    whereClause += ` AND DATE(${dbColumn}) = ?`;
                    whereParams.push(filterDate);
                    break;
                case 'lessThan':
                    whereClause += ` AND DATE(${dbColumn}) < ?`;
                    whereParams.push(filterDate);
                    break;
                case 'greaterThan':
                    whereClause += ` AND DATE(${dbColumn}) > ?`;
                    whereParams.push(filterDate);
                    break;
                case 'inRange':
                    const dateTo = moment(filter.dateTo).format('YYYY-MM-DD');
                    whereClause += ` AND DATE(${dbColumn}) BETWEEN ? AND ?`;
                    whereParams.push(filterDate, dateTo);
                    break;
                default:
                    break;
            }
        } else {
            whereClause += ` AND ${dbColumn} LIKE ?`;
            whereParams.push(`%${filter.filter}%`);
        }
    }

    return { whereClause, whereParams };
};

const buildOrderByClause = (sortModel, columnMap, defaultClause) => {
    if (!sortModel || sortModel.length === 0) return defaultClause;

    const orders = [];

    for (const sort of sortModel) {
        const dbCol = columnMap[sort.colId];
        if (dbCol) {
            const dir = sort.sort.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
            orders.push(`${dbCol} ${dir}`);
        }
    }

    return orders.length > 0 ? `ORDER BY ${orders.join(', ')}` : defaultClause;
};

export { buildWhereClauseAndParams, buildOrderByClause };
