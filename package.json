{"name": "sakai-react", "version": "10.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint"}, "dependencies": {"@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "ag-grid-community": "^33.3.2", "ag-grid-react": "^33.3.2", "axios": "^1.9.0", "bootstrap": "^5.3.6", "chart.js": "4.2.1", "moment": "^2.30.1", "mysql2": "^3.14.3", "next": "13.4.8", "odbc": "^2.4.9", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "^10.9.6", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.1.3", "winston": "^3.17.0"}, "devDependencies": {"eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}