const API_BASE = '/api/jadwal';

async function getJadwal(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    })

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getJadwalById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

export { getJadwal, getJadwalById };
