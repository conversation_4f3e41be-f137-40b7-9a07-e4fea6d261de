const API_BASE = '/api/kota';

async function getKota(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    });

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getKotaById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createKota(data) {
    const res = await fetch(API_BASE, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function updateKota(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function deleteKota(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });
    return res.json();
}

export { getKota, getKotaById, createKota, updateKota, deleteKota };
