const API_BASE = '/api/consignees';

async function getConsignees(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    })

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getConsigneeById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createConsignee(data) {
    const res = await fetch(API_BASE, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function updateConsignee(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function deleteConsignee(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });

    return res.json();
}

export { getConsignees, getConsigneeById, createConsignee, updateConsignee, deleteConsignee };
