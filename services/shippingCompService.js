const API_BASE = '/api/shipping-comps';

async function getShippingComps(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    })

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getShippingCompById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createShippingComp(data) {
    const res = await fetch(API_BASE, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function updateShippingComp(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function deleteShippingComp(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });

    return res.json();
}

export { getShippingComps, getShippingCompById, createShippingComp, updateShippingComp, deleteShippingComp };
