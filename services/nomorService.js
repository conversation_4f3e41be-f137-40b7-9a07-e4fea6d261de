const API_BASE = 'http://localhost:3000/api/nomor';

async function getNomor({ nama = '' } = {}) {
    const res = await fetch(`${API_BASE}?nama=${nama}`);
    return res.json();
}

async function updateNomor(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });
    return res.json();
}

export { getNomor, updateNomor };
