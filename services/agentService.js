const API_BASE = '/api/agents';

async function getAgents(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    })

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getAgentById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createAgent(data) {
    const res = await fetch(API_BASE, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function updateAgent(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function deleteAgent(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });

    return res.json();
}

export { getAgents, getAgentById, createAgent, updateAgent, deleteAgent };
