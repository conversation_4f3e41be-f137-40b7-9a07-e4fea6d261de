const API_BASE = '/api/truk';

async function getTruk(start, end, filters = {}, sortModel = []) {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    })

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return res.json();
}

async function getTrukById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createTruk(data) {
    const res = await fetch(API_BASE, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function updateTruk(id, data) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    return res.json();
}

async function deleteTruk(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });

    return res.json();
}

export { getTruk, getTrukById, createTruk, updateTruk, deleteTruk };
