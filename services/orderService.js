const API_BASE = '/api/orders';

const fetchOrders = async (start, end, filters = {}, sortModel = []) => {
    const queryParams = new URLSearchParams({
        start: start.toString(),
        end: end.toString(),
        filter: JSON.stringify(filters),
        sort: JSON.stringify(sortModel)
    });

    const res = await fetch(`${API_BASE}?${queryParams.toString()}`);
    return await res.json();
};

async function fetchOrderById(id) {
    const res = await fetch(`${API_BASE}/${id}`);
    return res.json();
}

async function createOrder({ master, details, nomor }) {
    const res = await fetch(`${API_BASE}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ master, details, nomor })
    });

    return res.json();
}

async function updateOrder(id, {master, details}) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ master, details })
    });

    return res.json();
}

async function deleteOrder(id) {
    const res = await fetch(`${API_BASE}/${id}`, {
        method: 'DELETE'
    });

    return res.json();
}

export { fetchOrders, fetchOrderById, createOrder, updateOrder, deleteOrder };
